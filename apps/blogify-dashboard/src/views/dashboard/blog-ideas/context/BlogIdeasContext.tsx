import { createContext, useState, type PropsWithChildren } from 'react';
import { IdeaResearch } from '../types/idea-research.type';

export interface BlogIdeasContextType {
  ideaResearchData: IdeaResearch | null;
  setResearchIdea: (idea: IdeaResearch) => void;
  removeResearchIdea: () => void;
  updateResearchIdea: (newData: Partial<IdeaResearch>) => void;
}

const BlogIdeasContext = createContext<BlogIdeasContextType | undefined>(undefined);

const BlogIdeasProvider = ({ children }: PropsWithChildren) => {
  const [ideaResearchData, setIdeaResearchData] = useState<IdeaResearch | null>(null);

  const setResearchIdea = (research: IdeaResearch) => setIdeaResearchData(research);

  const updateResearchIdea = (newData: Partial<IdeaResearch>) => {
    setIdeaResearchData((prev) => (prev ? ({ ...prev, ...newData } as IdeaResearch) : null));
  };

  const removeResearchIdea = () => setIdeaResearchData(null);

  const value = {
    ideaResearchData,
    setResearchIdea,
    removeResearchIdea,
    updateResearchIdea,
  };

  return <BlogIdeasContext.Provider value={value}>{children}</BlogIdeasContext.Provider>;
};

export default BlogIdeasProvider;

export { BlogIdeasContext };
