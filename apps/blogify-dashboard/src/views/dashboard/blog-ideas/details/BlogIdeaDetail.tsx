import { Outlet, Link, useParams, useLocation } from 'react-router-dom';
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@ps/ui/components/tabs';
import DashboardContainer from '../../layout/DashboardContainer';
import { useQuery } from 'react-query';
import { Project } from '../types/project.type';
import StateHandler from '@/components/misc/StateHandler';
import { Button } from '@ps/ui/components/button';
import { HiBeaker, HiOutlineLightBulb } from 'react-icons/hi2';

const TABS = [
  { name: 'Website Status', path: '' },
  { name: 'Blog Ideas', path: 'blog-ideas' },
  { name: 'Keywords', path: 'keywords' },
];

export default function BlogIdeaDetail() {
  const { id } = useParams() as { id: string };
  const location = useLocation();
  const pathName = location.pathname.split('/').pop() || '';
  const selectedTab = TABS.some((tab) => tab.path === pathName) ? pathName : TABS[0].path;

  const { data: ProjectData, isLoading, error } = useQuery<Project>(`projects/${id}`);

  return isLoading || error || !ProjectData ? (
    <StateHandler
      loading={isLoading}
      error={error as string}
      isEmpty={!ProjectData}
      emptyMsg="No project found."
    />
  ) : (
    <DashboardContainer
      title={`Project: ${ProjectData?.title || ProjectData?.name}`}
      actions={
        <>
          <Link to="/dashboard/blog-ideas/quick-ideas">
            <Button variant="secondary">
              <HiOutlineLightBulb />
              Quick Ideas
            </Button>
          </Link>

          <Button className="bg-primary text-white hover:bg-[#d24c25]" disabled>
            <HiBeaker className="mr-2 size-4 text-white" /> Research
          </Button>
        </>
      }
    >
      <Tabs defaultValue={selectedTab}>
        <TabsList variant="nav">
          {TABS.map((tab) => (
            <Link to={`./${tab.path}`} key={tab.path}>
              <TabsTrigger value={tab.path} variant="nav">
                {tab.name}
              </TabsTrigger>
            </Link>
          ))}
        </TabsList>

        <Outlet context={{ ProjectData }} />
      </Tabs>
    </DashboardContainer>
  );
}
