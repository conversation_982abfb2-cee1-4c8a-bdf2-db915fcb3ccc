import { APIResponse } from '@ps/types';
import { useMutation, useQuery } from 'react-query';
import { IdeaResearch } from '../types/idea-research.type';
import StateHandler from '@/components/misc/StateHandler';
import IdeaResearchCard from '../components/IdeaResearchCard';
import { FaEdit, FaTrash } from 'react-icons/fa';
import ConfirmationDialog from '@/components/dialog/ConfirmationDialog';
import { useToggle } from 'react-use';
import { useNavigate, useOutletContext } from 'react-router-dom';
import { useIdeaSelection } from '../hooks/useIdeaSelection';
import { useState } from 'react';
import { Project } from '../types/project.type';
import { API } from '@/services/api';
import toast from 'react-hot-toast';

export default function BlogIdeaIdeas() {
  const { ProjectData } = useOutletContext<{ ProjectData: Project }>();
  const { selectedIdeas, toggleIdeaSelection } = useIdeaSelection();

  const navigate = useNavigate();
  const [isConfirmationDialogOpen, toggleConfirmationDialogOpen] = useToggle(false);
  const [selectedResearchForDeletion, setSelectedResearchForDeletion] = useState<string | null>(
    null
  );

  // API Calls
  const getIdeaResearch = useQuery<APIResponse<IdeaResearch>>(
    `idea-research?projectId=${ProjectData._id}`
  );
  const ideaResearchData = getIdeaResearch.data?.data || [];

  const updateIdeaResearch = useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<IdeaResearch> }) =>
      API.patch(`idea-research/${id}`, data),
    onSuccess: () => {
      getIdeaResearch.refetch();
      toast.success('Blog idea updated successfully');
    },
    onError: (error) => {
      console.error('Update idea research error:', error);
      toast.error('Failed to update blog idea');
    },
  });
  const deleteIdeaResearch = useMutation({
    mutationFn: (id: string) => API.remove(`idea-research/${id}`),
    onSuccess: () => {
      getIdeaResearch.refetch();
      toast.success('Blog idea research deleted successfully');
    },
    onError: (error) => {
      console.error('Delete idea research error:', error);
      toast.error('Failed to delete research');
    },
  });

  // Handlers
  const handleIdeaDelete = async (research: IdeaResearch, idea: string) => {
    const newIdeas = research.ideas.filter((i) => i !== idea);
    updateIdeaResearch.mutate({
      id: research._id,
      data: { ideas: newIdeas },
    });
  };
  const handleUpdateIdeaResearch = (research: IdeaResearch) => {
    navigate('/dashboard/blog-ideas/quick-ideas', {
      state: {
        prompt: research.prompt,
        keywords: research?.keywords?.join(', '),
      },
    });
  };
  const handleDeleteResearch = (researchId: string) => {
    setSelectedResearchForDeletion(researchId);
    toggleConfirmationDialogOpen();
  };
  const confirmDeleteResearch = () => {
    if (selectedResearchForDeletion) {
      deleteIdeaResearch.mutate(selectedResearchForDeletion);
      setSelectedResearchForDeletion(null);
      toggleConfirmationDialogOpen();
    }
  };
  const handleGenerateAllBlogs = () => {
    navigate('/dashboard/blogs/select-source/bulk-create', {
      state: { ideas: selectedIdeas },
    });
  };

  return (
    <div className="mt-6 space-y-6">
      {getIdeaResearch.isLoading || getIdeaResearch.error || ideaResearchData.length === 0 ? (
        <StateHandler
          loading={getIdeaResearch.isLoading}
          error={getIdeaResearch.error as string}
          isEmpty={ideaResearchData.length === 0}
          emptyMsg="No ideas found."
        />
      ) : (
        ideaResearchData.map((research, i) => {
          const popoverActions = [
            {
              label: 'Update Research',
              icon: FaEdit,
              onClick: () => handleUpdateIdeaResearch(research),
            },
            {
              label: 'Delete Results',
              icon: FaTrash,
              onClick: () => handleDeleteResearch(research._id),
              className: 'text-red-600 hover:text-red-600',
            },
          ];

          return (
            <div key={research._id + i}>
              <IdeaResearchCard
                ideaResearchData={research}
                toggleIdeaSelection={toggleIdeaSelection}
                selectedIdeas={selectedIdeas}
                primaryAction={
                  selectedIdeas.length > 0
                    ? {
                        label: selectedIdeas.length > 1 ? 'GENERATE ALL BLOGS' : 'GENERATE BLOG',
                        onClick: handleGenerateAllBlogs,
                        loading: false,
                      }
                    : undefined
                }
                popoverActions={popoverActions}
                onDelete={(idea) => handleIdeaDelete(research, idea)}
              />

              {isConfirmationDialogOpen && (
                <ConfirmationDialog
                  isOpen={isConfirmationDialogOpen}
                  confirmationMessage={'Are you sure you want to delete this blog?'}
                  onClose={() => {
                    setSelectedResearchForDeletion(null);
                  }}
                  onConfirm={confirmDeleteResearch}
                  confirming={deleteIdeaResearch.isLoading}
                />
              )}
            </div>
          );
        })
      )}
    </div>
  );
}
