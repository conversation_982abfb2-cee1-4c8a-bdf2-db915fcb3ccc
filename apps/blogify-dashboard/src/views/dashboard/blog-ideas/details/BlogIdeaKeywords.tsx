import type { KeywordIdea } from '../types/google-ads.type';
import type { Project } from '../types/project.type';

import { useMutation, useQuery } from 'react-query';
import { useEffect, useState } from 'react';
import { BiLoaderAlt } from 'react-icons/bi';
import { Link, useParams } from 'react-router-dom';

import { useStoreActions } from '@/store';
import { useStoreState } from '@/store';
import { cacheGet } from '@/utils/localStorageCache';
import { Button } from '@ps/ui/components/button';
import { API } from '@/services/api';
import Select from '@ps/ui/form/Select';
import config from '@/constants/config';

import KeywordResearchSearchBox from '../blog-idea-keyword/components/KeywordResearchSearchBox';
import KeywordResearchDataTable from '../blog-idea-keyword/components/KeywordResearchDataTable';

export default function BlogIdeaKeywords() {
  const user = useStoreState((s) => s.user.current);

  return (
    <>
      {user.googleAdsStatus === 'disconnected' && <GoogleAddsNotConnected />}
      {user.googleAdsStatus === 'pending' && <GoogleAdsSelectCustomerId />}
      {user.googleAdsStatus === 'connected' && <KeywordResearch />}
    </>
  );
}

const GoogleAddsNotConnected = () => (
  <div className="flex h-[calc(100vh-295px)] flex-col gap-3 text-center flex-center">
    <h3 className="text-lg font-semibold">Connect Your Google Ads Account</h3>

    <p className="text-md">
      We make the use of Google Ads Keyword Planer to provide you with keyword ideas.
      <br /> You need to connect your Google Ads account to use this feature.
    </p>

    <div className="mt-2">
      <Link
        to={`${config.apiUrl}google-ads/connect?token=${cacheGet('token')}&redirectTo=${window.location.origin}${window.location.pathname}`}
      >
        <Button className="flex items-center justify-center">Connect Google Ads</Button>
      </Link>
    </div>
  </div>
);

const GoogleAdsSelectCustomerId = () => (
  <div className="flex h-[calc(100vh-295px)] flex-col gap-3 text-center flex-center">
    <h3 className="text-lg font-semibold">Select Your Google Ads Account</h3>

    <p className="text-md">Please select your Google Ads account to complete the connection.</p>

    <div className="mt-2">
      <GoogleAdsCustomerIdSelector />
    </div>
  </div>
);

const GoogleAdsCustomerIdSelector = () => {
  const [customerId, setCustomerId] = useState<string>('');

  const { data: customers } = useQuery<string[]>('google-ads/customers');
  const fetchUser = useStoreActions((a) => a.user.fetch);
  const user = useStoreState((a) => a.user.current);

  const { mutate: selectCustomerId } = useMutation({
    mutationFn: (id: string) =>
      API.post('google-ads/customers/select', { customerId: id }).then(() => fetchUser()),
  });

  useEffect(() => {
    setCustomerId(user.googleAdsCustomerId || '');
  }, [user.googleAdsCustomerId]);

  return (
    <Select
      className="pr-8"
      variant="secondary"
      value={customerId}
      onChange={(e) => {
        setCustomerId(e.target.value);
        selectCustomerId(e.target.value);
      }}
    >
      <option value="">Google Ads account</option>
      {customers?.map((customer) => (
        <option key={customer} value={customer}>
          {customer}
        </option>
      ))}
    </Select>
  );
};

const KeywordResearch = () => {
  const fetchUser = useStoreActions((a) => a.user.fetch);
  const { id } = useParams() as { id: string };
  const { data: project } = useQuery<Project>(`projects/${id}`);

  const {
    data: keywordIdeas = [],
    mutate: searchKeywords,
    isLoading,
    error,
  } = useMutation({
    mutationFn: (payload: any) =>
      API.post<KeywordIdea[]>('google-ads/keyword-ideas', { ...payload, url: project?.websiteUrl }),
  });

  const { mutate: disconnect, isLoading: isDisconnecting } = useMutation({
    mutationFn: () => API.remove('google-ads/disconnect').then(() => fetchUser()),
  });

  return (
    <>
      <div className="flex items-center justify-between">
        <h2 className="pb-3 pt-6 text-17 font-semibold">Search Keywords</h2>

        <div className="flex items-center gap-2">
          <GoogleAdsCustomerIdSelector />
          <Button onClick={() => disconnect()} loading={isDisconnecting} variant="danger" size="sm">
            Disconnect Google Ads
          </Button>
        </div>
      </div>

      <KeywordResearchSearchBox onSearch={searchKeywords} />

      <div className="mt-4">
        {error ? (
          <p className="min-h-14 border-l-4 border-red4 bg-red4/20 p-4">
            <span className="font-bold">Error:</span> {error as string}
          </p>
        ) : isLoading ? (
          <div className="flex min-h-14 items-center flex-center">
            <BiLoaderAlt className="mr-2 animate-spin" />
            Loading...
          </div>
        ) : (
          <KeywordResearchDataTable keywordIdeas={keywordIdeas} />
        )}
      </div>
    </>
  );
};
