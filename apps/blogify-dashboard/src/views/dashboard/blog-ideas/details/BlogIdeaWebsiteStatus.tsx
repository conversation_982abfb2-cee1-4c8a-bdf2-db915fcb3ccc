import { useOutletContext } from 'react-router-dom';
import { ImLink } from 'react-icons/im';
import { Project } from '../types/project.type';
import { PiPlugsConnected } from 'react-icons/pi';
import { FaUsers } from 'react-icons/fa';

export default function BlogIdeaWebsiteStatus() {
  const { ProjectData } = useOutletContext<{ ProjectData: Project }>();

  return (
    <div className=" mx-auto p-6 ">
      {/* Title and URL */}
      <div>
        <h2 className="text-2xl font-bold">{ProjectData.name}</h2>
        <a href="#" className="text-xs font-semibold text-[#93766c] hover:underline">
          {ProjectData.websiteUrl}
        </a>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 gap-4 py-8 md:grid-cols-3">
        {[
          {
            icon: <FaUsers className="size-8 text-primary" />,
            label: 'Organic Traffic',
            value: Math.floor(ProjectData.domainAnalytics?.metrics.organic.etv || 0),
          },
          {
            icon: <PiPlugsConnected className="size-8 text-primary" />,
            label: 'Referring Domains',
            value: Math.floor(ProjectData.domainAnalytics?.backlinks_info.referring_domains || 0),
          },
          {
            icon: <ImLink className="size-8 text-primary" />,
            label: 'Backlinks',
            value: Math.floor(ProjectData.domainAnalytics?.backlinks_info.backlinks || 0),
          },
        ].map((stat, idx) => (
          <div key={idx} className="flex items-center space-x-4 rounded-lg border p-4">
            <div className="rounded-lg bg-orange-100 p-4">{stat.icon}</div>
            <div>
              <p className="text-sm font-medium">{stat.label}</p>
              <p className="text-2xl font-bold">{stat.value}</p>
            </div>
          </div>
        ))}
      </div>

      {/* About Section */}
      <div className="flex items-center justify-between gap-4 py-8">
        <div className="">
          {ProjectData?.description && (
            <>
              <h3 className="mb-2 text-xl font-bold">About</h3>
              <p className="text-gray-700">{ProjectData.description}</p>
            </>
          )}

          {/* Topics Section */}
          {ProjectData.keywords && ProjectData.keywords.length > 0 && (
            <div className="mt-8">
              <h3 className="mb-4 text-xl font-bold">Topics</h3>
              <div className="flex flex-wrap gap-2">
                {ProjectData?.keywords?.map((topic, index) => (
                  <div key={topic + index} className="flex items-center">
                    <div className="flex items-center rounded-md border p-1 text-sm font-medium">
                      {topic}
                      {/* {topic.blogCount > 0 && (
                      <Badge variant="successDark" className="ml-2 border-0 text-white">
                        {topic.blogCount} Blogs
                      </Badge>
                    )} */}
                      {/* {index > 0 && (
                      <button
                        onClick={() => removeTopic(topic.id)}
                        className="py-.5 rounded-lg px-1 hover:bg-gray-100"
                      >
                        <FiX size={16} />
                      </button>
                    )} */}
                    </div>
                  </div>
                ))}
                {/* <button className="flex items-center gap-1 rounded-lg border p-1 text-sm text-gray-500 hover:bg-gray-100">
                <FiPlusCircle size={16} />
                Add Topic
              </button> */}
              </div>
            </div>
          )}
        </div>

        {ProjectData.featuredImage && (
          <img
            src={ProjectData.featuredImage}
            alt={ProjectData?.title || ProjectData.name}
            className="h-[300px] w-[736px] rounded-lg"
          />
        )}
      </div>
    </div>
  );
}
