/* eslint-disable react-refresh/only-export-components */
import { Outlet, type RouteObject } from 'react-router-dom';
import { Suspense } from 'react';

import FullPageSpinner from '@/components/misc/FullPageSpinner';
import safeLazy from '@/utils/safeLazy';
import BlogIdeasProvider from './context/BlogIdeasContext';

const BlogIdeaList = safeLazy(() => import('./BlogIdeaList'));
const BlogIdeaDetail = safeLazy(() => import('./details/BlogIdeaDetail'));
const BlogIdeaWebsiteStatus = safeLazy(() => import('./details/BlogIdeaWebsiteStatus'));
const BlogIdeaIdeas = safeLazy(() => import('./details/BlogIdeaIdeas'));
const BlogIdeaKeywords = safeLazy(() => import('./details/BlogIdeaKeywords'));
const BlogIdeasNewProject = safeLazy(() => import('./NewProject'));
const BlogIdeasQuickIdeas = safeLazy(() => import('./QuickIdeas'));

const Lazy = ({ as: Component }: { as: React.ComponentType<any> }) => (
  <Suspense fallback={<FullPageSpinner />}>
    <Component />
  </Suspense>
);

function BlogIdeaRoot() {
  return (
    <BlogIdeasProvider>
      <Outlet />
    </BlogIdeasProvider>
  );
}

const getBlogIdeasRoutes = (): RouteObject[] => [
  {
    path: 'blog-ideas',
    element: <BlogIdeaRoot />,
    children: [
      { path: '', element: <Lazy as={BlogIdeaList} /> },
      { path: 'new-project', element: <Lazy as={BlogIdeasNewProject} /> },
      { path: 'quick-ideas', element: <Lazy as={BlogIdeasQuickIdeas} /> },
      {
        path: ':id',
        element: <Lazy as={BlogIdeaDetail} />,
        children: [
          { path: '', element: <Lazy as={BlogIdeaWebsiteStatus} /> },
          { path: 'blog-ideas', element: <Lazy as={BlogIdeaIdeas} /> },
          { path: 'keywords', element: <Lazy as={BlogIdeaKeywords} /> },
        ],
      },
    ],
  },
];

export default getBlogIdeasRoutes;
