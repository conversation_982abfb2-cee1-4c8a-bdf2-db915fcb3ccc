import { API } from '@/services/api';
import { z, zodResolver } from '@ps/ui';
import { Button } from '@ps/ui/components/button';
import <PERSON><PERSON>ield from '@ps/ui/form/FormField';
import toast from 'react-hot-toast';
import { useMutation } from 'react-query';
import { IdeaResearch } from '../types/idea-research.type';
import { useForm } from '@ps/ui/hooks/useForm';
import { useBlogIdeas } from '../context/useBlogIdeas';

const quickIdeasSchema = z.object({
  prompt: z.string({ required_error: 'Prompt is required.' }),
  keywords: z.string().optional(),
});

export type QuickIdeasSchema = z.infer<typeof quickIdeasSchema>;

type PropsType = {
  defaultValues?: QuickIdeasSchema;
};

const QuickIdeasForm = ({ defaultValues }: PropsType) => {
  const { setResearchIdea } = useBlogIdeas();

  const { getInputFields, handleSubmit } = useForm<QuickIdeasSchema>({
    resolver: zod<PERSON><PERSON>olver(quickIdeasSchema),
    defaultValues,
  });

  const { mutateAsync: onIdeaResearch, isLoading } = useMutation({
    mutationFn: (payload: { prompt: string; keywords: string[] }) =>
      API.post<IdeaResearch>(`idea-research/generate-ideas`, payload),
  });

  const submit = (values: QuickIdeasSchema) => {
    const payload = {
      ...values,
      keywords: values.keywords ? values.keywords.split(',').map((keyword) => keyword.trim()) : [],
    };
    onIdeaResearch(payload)
      .then((researchData) => setResearchIdea(researchData as IdeaResearch))
      .catch((err) => {
        toast.error('Something went wrong.');
        console.error(err);
      });
  };

  return (
    <form onSubmit={handleSubmit(submit)}>
      <div className="space-y-2">
        <FormField
          type="textarea"
          label="Prompt"
          placeholder="eg. Use simple & easy to understand sentences."
          {...getInputFields('prompt')}
        />

        <FormField
          label="SEO Keywords (Optional)"
          placeholder="eg. Apple Watch Ultra 2, Adventure Watch, Tough"
          {...getInputFields('keywords')}
        />

        <Button
          className="mt-5 w-full bg-primary hover:bg-[#d24c25]"
          type="submit"
          loading={isLoading}
          disabled={isLoading}
        >
          Suggest Blog Ideas
        </Button>
      </div>
    </form>
  );
};

export default QuickIdeasForm;
