/* eslint-disable tailwindcss/enforces-negative-arbitrary-values */
import { APIResponse } from '@ps/types';
import Dialog from '@ps/ui/components/common/dialogs/Dialog';
import { FaBookmark } from 'react-icons/fa';
import { Project } from '../types/project.type';
import { useQuery } from 'react-query';
import StateHandler from '@/components/misc/StateHandler';
import { Button } from '@ps/ui/components/button';
import { RadioGroup, RadioGroupItem } from '@ps/ui/components/radio-group';
import { cn } from '@ps/ui/lib/utils';
import { useBlogIdeas } from '../context/useBlogIdeas';

type PropsType = {
  isOpen: boolean;
  toggleDialog: () => void;
  onSave: () => void;
};

const SelectProjectDialog = ({ isOpen, toggleDialog, onSave }: PropsType) => {
  const { updateResearchIdea } = useBlogIdeas();

  const {
    data: { data: projectData } = { data: [] },
    isLoading,
    error,
  } = useQuery<APIResponse<Project>>('projects');

  const handleSave = () => {
    onSave();
    toggleDialog();
  };

  return (
    <Dialog
      isOpen={isOpen}
      onClose={toggleDialog}
      title="Save Blog Ideas"
      description="Select a project where you want to save your blog ideas."
      PrimaryIcon={FaBookmark}
      primaryButton={{
        label: 'Save Blog Ideas',
        onClick: handleSave,
      }}
    >
      <p className="mb-1 text-sm font-semibold">Select a Project</p>

      {isLoading || error ? (
        <StateHandler loading={isLoading} error={error as string} />
      ) : projectData.length === 0 ? (
        <p className="flex h-[200px] w-full items-center justify-center rounded-lg border text-sm">
          No Project Available
        </p>
      ) : (
        <RadioGroup
          className="gap-0 divide-y rounded-lg border border-gray10"
          onValueChange={(projectId) => updateResearchIdea({ project: projectId })}
        >
          {projectData.map((project) => (
            <label
              key={project._id + project.name}
              className={cn('flex gap-x-3.5 border-gray10 p-4')}
            >
              <RadioGroupItem value={project._id} />

              <div className="-mt-[6px] font-medium">
                <h5>{project.name}</h5>
                <h6 className="mt-1.5 text-sm  text-gray9">{project.websiteUrl}</h6>
              </div>
            </label>
          ))}
        </RadioGroup>
      )}

      <Button variant="outline" className="mt-2 h-fit px-[8px] py-[3px] text-sm text-gray9">
        + New Project
      </Button>
    </Dialog>
  );
};

export default SelectProjectDialog;
