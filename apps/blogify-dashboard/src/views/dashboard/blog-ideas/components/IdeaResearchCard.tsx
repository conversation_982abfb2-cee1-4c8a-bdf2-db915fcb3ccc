import { Button } from '@ps/ui/components/button';
import { Popover, PopoverContent, PopoverTrigger } from '@ps/ui/components/popover';
import { FaEllipsisV } from 'react-icons/fa';
import { IdeaResearch, PopoverAction } from '../types/idea-research.type';
import dayjs from 'dayjs';
import { cn } from '@ps/ui/lib/utils';
import IdeaItem from './IdeaItem';

interface ActionButton {
  label: string;
  onClick: () => void;
  loading: boolean;
}

type PropsType = {
  ideaResearchData: IdeaResearch;
  toggleIdeaSelection: (idea: string) => void;
  selectedIdeas: string[];
  primaryAction?: ActionButton;
  secondaryAction?: ActionButton;
  popoverActions: PopoverAction[];
  onDelete: (idea: string) => void;
};

const IdeaResearchCard = ({
  ideaResearchData,
  toggleIdeaSelection,
  selectedIdeas,
  primaryAction,
  secondaryAction,
  popoverActions,
  onDelete,
}: PropsType) => (
  <div className="rounded-lg border bg-gray-50">
    <div className="flex flex-col gap-2 border-b bg-[#f4f1f0]  p-4">
      <div className="flex flex-wrap items-center justify-between gap-2">
        <div className="flex flex-col">
          <div className="font-bold">Quick Ideas ({ideaResearchData.ideas.length})</div>
          <div className="text-sm text-[#93766c]">
            Researched On : {dayjs(ideaResearchData.researchDateTime).format('D MMM YYYY')}
          </div>
        </div>

        {popoverActions.length > 0 && (
          <Popover>
            <PopoverTrigger asChild>
              <button className="text-gray-400">
                <FaEllipsisV size={14} />
              </button>
            </PopoverTrigger>
            <PopoverContent className="w-40 p-0" side="right" align="start">
              <div className="w-56 rounded-xl border bg-white py-1 shadow-md">
                {popoverActions.map((action) => (
                  <Button
                    key={action.label}
                    variant="ghost"
                    className={cn(
                      'w-full justify-start px-3 py-2 font-medium text-black hover:bg-gray-100',
                      action.className
                    )}
                    onClick={action.onClick}
                  >
                    <action.icon size={16} className="mr-2" />
                    {action.label}
                  </Button>
                ))}
              </div>
            </PopoverContent>
          </Popover>
        )}
      </div>

      <div className="flex flex-wrap gap-2 py-2">
        {ideaResearchData?.prompt && (
          <span className="rounded-lg bg-white px-3 py-1 text-sm">{ideaResearchData.prompt}</span>
        )}
        {ideaResearchData?.keywords &&
          ideaResearchData?.keywords.map((keyword) => (
            <span key={keyword} className="rounded-lg bg-white px-3 py-1 text-sm">
              {keyword}
            </span>
          ))}
      </div>
    </div>

    {/* Ideas list */}
    <div className="bg-white">
      {ideaResearchData.ideas.map((idea) => (
        <IdeaItem
          key={idea}
          title={idea}
          selected={selectedIdeas.includes(idea)}
          onSelect={() => toggleIdeaSelection(idea)}
          onDelete={() => onDelete(idea)}
        />
      ))}
    </div>

    {/* Action buttons */}
    {(primaryAction || secondaryAction) && (
      <div className="flex gap-2 bg-white p-3">
        {primaryAction && (
          <Button
            size="xs"
            className="w-full rounded-md bg-primary text-sm hover:bg-orange-600 sm:w-auto"
            onClick={primaryAction.onClick}
            disabled={primaryAction.loading}
            loading={primaryAction.loading}
          >
            {primaryAction.label}
          </Button>
        )}

        {secondaryAction && (
          <Button
            variant="outline"
            size="xs"
            className="w-full rounded-md text-sm sm:w-auto"
            onClick={secondaryAction.onClick}
            disabled={secondaryAction.loading}
            loading={secondaryAction.loading}
          >
            {secondaryAction.label}
          </Button>
        )}
      </div>
    )}
  </div>
);

export default IdeaResearchCard;
