import { Checkbox } from '@ps/ui/components/checkbox';
import { Popover, PopoverContent, PopoverTrigger } from '@ps/ui/components/popover';
import { FaEllipsisV, FaTrash } from 'react-icons/fa';

type IdeaItemProps = {
  title: string;
  selected: boolean;
  onSelect: () => void;
  onDelete?: () => void;
};

const IdeaItem = ({ title, selected, onSelect, onDelete }: IdeaItemProps) => (
  <div className="flex flex-wrap items-center justify-between gap-2 border-b border-gray-100 p-3">
    <div className="flex min-w-0 flex-1 items-center gap-3">
      <Checkbox
        checked={selected}
        onCheckedChange={onSelect}
        className="size-4 shrink-0 accent-orange-500"
      />
      <span className={`break-words ${selected ? 'font-medium' : 'text-gray-700'}`}>{title}</span>
    </div>

    {onDelete && (
      <Popover>
        <PopoverTrigger asChild>
          <button className="shrink-0 text-gray-400">
            <FaEllipsisV size={14} />
          </button>
        </PopoverTrigger>
        <PopoverContent className="w-40 p-0" side="right" align="start">
          <div className="py-1">
            <button
              onClick={onDelete}
              className="flex w-full items-center gap-2 px-4 py-2 text-left text-sm text-[#c02] hover:bg-gray-100"
            >
              <FaTrash size={12} />
              <span>Delete</span>
            </button>
          </div>
        </PopoverContent>
      </Popover>
    )}
  </div>
);

export default IdeaItem;
