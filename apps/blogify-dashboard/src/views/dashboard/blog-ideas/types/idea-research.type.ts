import { Base } from '@ps/types/resources/base.type';

export interface IdeaResearch extends Base {
  project?: string;
  prompt?: string;
  keywords?: string[];
  ideas: string[];
  researchDateTime: string;
}

export interface QuickIdeasFormData {
  prompt: string;
  keywords: string;
}

export interface PopoverAction {
  label: string;
  icon: React.ComponentType<{ size?: number; className?: string }>;
  onClick: () => void;
  className?: string;
}
