import { Base } from '@/types/resources/base.type';

interface DomainMetrics {
  pos_1: number;
  pos_2_3: number;
  pos_4_10: number;
  pos_11_20: number;
  pos_21_30: number;
  pos_31_40: number;
  pos_41_50: number;
  pos_51_60: number;
  pos_61_70: number;
  pos_71_80: number;
  pos_81_90: number;
  pos_91_100: number;
  etv: number;
  count: number;
  estimated_paid_traffic_cost: number;
}

interface BacklinksInfo {
  referring_domains: number;
  referring_main_domains: number;
  referring_pages: number;
  dofollow: number;
  backlinks: number;
  time_update: string;
}

export interface DomainAnalytics {
  domain: string;
  created_datetime: string | null;
  changed_datetime: string;
  expiration_datetime: string | null;
  updated_datetime: string;
  first_seen: string;
  epp_status_codes: string[];
  tld: string;
  registered: boolean;
  registrar: string;
  metrics: {
    organic: DomainMetrics;
    paid: DomainMetrics;
  };
  backlinks_info: BacklinksInfo;
}

export interface Project extends Base {
  name: string;
  websiteUrl: string;
  title?: string;
  description?: string;
  featuredImage?: string;
  keywords?: string[];
  headings?: {
    h1: string[];
    h2: string[];
    h3: string[];
  };
  content?: string;
  domainAnalytics?: DomainAnalytics;
}
