import type { <PERSON>Rang<PERSON> } from 'react-day-picker';

import { FaRegCalendarAlt, FaSearch } from 'react-icons/fa';
import { FaLocationDot } from 'react-icons/fa6';
import { format, sub } from 'date-fns';
import { IoLanguage } from 'react-icons/io5';
import { useState } from 'react';

import {
  SelectContent,
  SelectTrigger,
  SelectValue,
  SelectItem,
  Select,
} from '@ps/ui/components/select';
import { PopoverContent, PopoverTrigger, Popover } from '@ps/ui/components/popover';
import { zodResolver, z } from '@ps/ui';
import { Calendar } from '@ps/ui/components/calendar';
import { TagInput } from '@ps/ui/components/tag-input';
import { useForm } from '@ps/ui/hooks/useForm';
import { Button } from '@ps/ui/components/button';
import { cn } from '@ps/ui/lib/utils';
import useCountryStateCity from '@/hooks/useCountryStateCity';

import { BLOG_LANGUAGES } from '../../../blog/create/utils/languages';

const keywordResearchSchema = z.object({
  keywords: z.array(z.string()).default([]),
  country: z.string().default('Global'),
  language: z.string().default('English'),
  date: z.object({
    from: z.string(),
    to: z.string(),
  }),
});
type KeywordResearchSchema = z.infer<typeof keywordResearchSchema>;

export default function KeywordResearchSearchBox({
  onSearch,
}: {
  onSearch: (v: KeywordResearchSchema) => void;
}) {
  const { watch, setValue, getValues } = useForm<KeywordResearchSchema>({
    resolver: zodResolver(keywordResearchSchema),
    defaultValues: {
      keywords: [],
      country: 'Global',
      language: 'English',
      date: {
        from: format(sub(new Date(), { years: 1 }), 'yyyy-MM-dd'),
        to: format(new Date(), 'yyyy-MM-dd'),
      },
    },
  });
  const [date, setDate] = useState<DateRange | undefined>({
    from: sub(new Date(), { years: 1 }),
    to: new Date(),
  });

  const { countries } = useCountryStateCity();

  const handleSearch = () => {
    const values = getValues();
    onSearch({
      ...values,
      date: {
        from: format(values.date?.from || new Date(), 'yyyy-MM-dd'),
        to: format(values.date?.to || new Date(), 'yyyy-MM-dd'),
      },
    });
  };

  return (
    <section className="rounded-lg border border-gray10">
      <div className="flex items-center gap-3 px-3 py-2">
        <FaSearch size={16} />

        <TagInput
          variant="blank"
          placeholder="Type keywords related to your products or services. Use comma-separation or hit enter."
          onChange={(v) => setValue('keywords', v)}
          value={watch('keywords')}
          type="tags"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
        <div className="border-t border-gray10 md:border-r">
          <Select value={watch('country')} onValueChange={(v) => setValue('country', v)}>
            <SelectTrigger className="border-none text-13 font-medium focus:ring-0 focus:ring-offset-0 md:text-15">
              <div className="flex items-center gap-2">
                <FaLocationDot />
                <SelectValue placeholder="Select Country">{watch('country')}</SelectValue>
              </div>
            </SelectTrigger>

            <SelectContent>
              <SelectItem value="Global">Global</SelectItem>
              {countries.map((c) => (
                <SelectItem key={c.id} value={c.iso2}>
                  {c.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="border-t border-gray10 lg:border-r">
          <Select value={watch('language')} onValueChange={(v) => setValue('language', v)}>
            <SelectTrigger className="border-none text-13 font-medium focus:ring-0 focus:ring-offset-0 md:text-15">
              <div className="flex items-center gap-2">
                <IoLanguage />
                <SelectValue placeholder="Select Language">{watch('language')}</SelectValue>
              </div>
            </SelectTrigger>

            <SelectContent>
              {BLOG_LANGUAGES.map((l, i) => (
                <SelectItem key={i} value={l.iso}>
                  {l.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="border-t border-gray10">
          <Popover>
            <PopoverTrigger asChild>
              <button
                className={cn('flex h-10 items-center gap-2.5 p-3 text-13 font-medium lg:text-15')}
              >
                <FaRegCalendarAlt />
                {date?.from ? (
                  date.to ? (
                    <>
                      {format(date.from, 'LLL, y')} - {format(date.to, 'LLL, y')}
                    </>
                  ) : (
                    format(date.from, 'LLL, y')
                  )
                ) : (
                  <span>Pick a date</span>
                )}
              </button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                initialFocus
                mode="range"
                defaultMonth={date?.from}
                selected={date}
                onSelect={setDate}
                numberOfMonths={2}
              />
            </PopoverContent>
          </Popover>
        </div>

        <div className="border-t border-primary sm:border-l">
          <Button className="size-full rounded-t-none sm:rounded-bl-none" onClick={handleSearch}>
            Search
          </Button>
        </div>
      </div>
    </section>
  );
}
