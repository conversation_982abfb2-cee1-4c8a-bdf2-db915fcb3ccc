import type { KeywordIdea } from '../../types/google-ads.type';

import {
  TableHeader,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Table,
} from '@ps/ui/components/table';
import { Badge } from '@ps/ui/components/badge';

const difficultyColor: Record<KeywordIdea['keyword_idea_metrics']['competition'], string> = {
  LOW: 'text-[#093] bg-[#093]/10',
  MEDIUM: 'text-[#088c90] bg-[#008c99]/10',
  HIGH: 'text-[#c8223d] bg-[#c8223d]/10',
};

export default function KeywordResearchDataTable({
  keywordIdeas,
}: {
  keywordIdeas: KeywordIdea[];
}) {
  if (!keywordIdeas.length)
    return (
      <div className="mt-4 flex flex-center">
        <div className="text-center text-lg">No keyword ideas found</div>
      </div>
    );

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Keyword</TableHead>
          <TableHead>Search Volume</TableHead>
          <TableHead>Difficulty</TableHead>
          <TableHead>CPC</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {keywordIdeas.map((idea) => (
          <TableRow key={idea.text}>
            <TableCell className="font-medium">{idea.text}</TableCell>
            <TableCell>{idea.keyword_idea_metrics?.avg_monthly_searches}</TableCell>
            <TableCell>
              <Badge className={difficultyColor.LOW}>
                {/* {idea.keyword_idea_metrics?.competition} */}
                LOW
              </Badge>
            </TableCell>
            <TableCell>
              ${((idea.keyword_idea_metrics?.low_top_of_page_bid_micros || 0) / 1000000).toFixed(2)}{' '}
              - $
              {((idea.keyword_idea_metrics?.high_top_of_page_bid_micros || 0) / 1000000).toFixed(2)}
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
}
