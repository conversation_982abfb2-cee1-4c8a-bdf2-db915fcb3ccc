import DashboardContainer from '../layout/DashboardContainer';
import { FiPlus } from 'react-icons/fi';
import BlogIdeasCard from './components/BlogIdeasCard';
import { Link } from 'react-router-dom';
import { useQuery } from 'react-query';
import { Project } from './types/project.type';
import { APIResponse } from '@ps/types';
import StateHandler from '@/components/misc/StateHandler';
import { Button } from '@ps/ui/components/button';
import { HiOutlineLightBulb } from 'react-icons/hi2';
import { FaFolder } from 'react-icons/fa';

export default function BlogIdeas() {
  const {
    data: { data: ProjectData } = { data: [] },
    isLoading,
    error,
    refetch: refetchProjects,
  } = useQuery<APIResponse<Project>>('projects');

  return (
    <DashboardContainer
      title="Blog Ideas"
      actions={
        <>
          <Link to="/dashboard/blog-ideas/quick-ideas">
            <Button variant="secondary">
              <HiOutlineLightBulb />
              Quick Ideas
            </Button>
          </Link>
          <Link to="/dashboard/blog-ideas/new-project">
            <Button className="bg-primary text-white hover:bg-[#d24c25]">
              <FaFolder className="mr-2 size-4 text-white" /> New Project
            </Button>
          </Link>
        </>
      }
    >
      {isLoading || error ? (
        <StateHandler loading={isLoading} error={error as string} />
      ) : (
        <div className="min-h-screen bg-white">
          <div className="mx-auto px-4 py-8 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5">
              {ProjectData.map((project) => (
                <BlogIdeasCard
                  key={project._id}
                  project={project}
                  refetchProjects={refetchProjects}
                />
              ))}

              <Link to="/dashboard/blog-ideas/new-project">
                <div className="relative overflow-hidden rounded-lg bg-white">
                  {/* Folder Top Shape */}
                  <svg viewBox="0 0 220 20" width="100%" height="40" className="block">
                    <path
                      d="M0 8 C0 3 3 0 8 0 H60 C65 0 68 3 70 8 H212 C216 8 220 12 220 16 V40 H0 Z"
                      fill="#E9E5E1"
                    />
                  </svg>

                  {/* Folder Body */}
                  <div className="rounded-b-lg border border-dashed bg-white p-10">
                    <foreignObject x="0" y="0" width="240" height="180">
                      <div className="flex size-full items-center justify-center">
                        <FiPlus className="mx-auto size-10 text-gray-300" />
                      </div>
                    </foreignObject>
                  </div>
                </div>
              </Link>
            </div>
          </div>
        </div>
      )}
    </DashboardContainer>
  );
}
