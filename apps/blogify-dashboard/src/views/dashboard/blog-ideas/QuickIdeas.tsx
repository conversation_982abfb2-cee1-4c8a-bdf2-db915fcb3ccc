import { Button } from '@ps/ui/components/button';
import DashboardContainer from '../layout/DashboardContainer';
import QuickIdeasForm from './components/QuickIdeasForm';
import { useBlogIdeas } from './context/useBlogIdeas';
import IdeaResearchCard from './components/IdeaResearchCard';
import SelectProjectDialog from './components/SelectProjectDialog';
import { useToggle } from 'react-use';
import { useNavigate, useLocation } from 'react-router-dom';
import { useState } from 'react';
import toast from 'react-hot-toast';
import { IdeaResearch, QuickIdeasFormData } from './types/idea-research.type';
import { FaEdit, FaSave, FaTrash } from 'react-icons/fa';
import { useIdeaSelection } from './hooks/useIdeaSelection';
import { useMutation } from 'react-query';
import { API } from '@/services/api';

const QuickIdeas = () => {
  const { ideaResearchData, removeResearchIdea, updateResearchIdea } = useBlogIdeas();
  const { selectedIdeas, toggleIdeaSelection, selectAllIdeas, clearSelection } = useIdeaSelection();

  const { state } = useLocation();
  const navigate = useNavigate();

  const [isSelectProjectDialogOpen, toggleSelectProjectDialog] = useToggle(false);
  const [quickIdeasFormData, setQuickIdeasFormData] = useState<QuickIdeasFormData>({
    prompt: state?.prompt || '',
    keywords: state?.keywords || '',
  });

  const createIdeaResearch = useMutation({
    mutationFn: (payload: IdeaResearch) => API.post<IdeaResearch>('idea-research', payload),
    onSuccess: (result) => {
      handleDeleteAll();
      navigate(`/dashboard/blog-ideas/${result?.project}/blog-ideas`);
    },
    onError: (error) => {
      console.error('Create idea research error:', error);
      toast.error('Failed to save blog ideas');
    },
  });

  const handleSave = async () => {
    if (!ideaResearchData?.project) {
      toggleSelectProjectDialog();
      return;
    }
    createIdeaResearch.mutate({
      ...ideaResearchData,
      ideas: selectedIdeas,
    });
  };

  const handleSaveAllIdeas = () => {
    if (ideaResearchData?.ideas) {
      selectAllIdeas(ideaResearchData.ideas);
      setTimeout(() => handleSave(), 0);
    }
  };

  const handleUpdateResearch = () => {
    if (ideaResearchData) {
      setQuickIdeasFormData({
        prompt: ideaResearchData.prompt || '',
        keywords: ideaResearchData.keywords?.join(', ') || '',
      });
      handleDeleteAll();
      navigate('/dashboard/blog-ideas/quick-ideas');
    }
  };

  const handleDeleteAll = () => {
    clearSelection();
    removeResearchIdea();
  };

  const handleCancel = () => {
    if (ideaResearchData) {
      handleDeleteAll();
    } else {
      navigate(-1);
    }
  };

  const handleDeleteIdea = (idea: string) => {
    if (ideaResearchData) {
      const newIdeas = ideaResearchData.ideas.filter((i) => i !== idea);
      updateResearchIdea({ ideas: newIdeas });
    }
  };

  const popoverActions = [
    {
      label: 'Save All Ideas',
      icon: FaSave,
      onClick: handleSaveAllIdeas,
    },
    {
      label: 'Update Research',
      icon: FaEdit,
      onClick: handleUpdateResearch,
    },
    {
      label: 'Delete Results',
      icon: FaTrash,
      onClick: handleDeleteAll,
      className: 'text-red-600 hover:text-red-600',
    },
  ];

  return (
    <DashboardContainer
      title={'Quick Ideas'}
      className="flex h-full flex-col"
      actions={
        <Button
          variant="outline"
          size="sm"
          className=" h-8 border bg-white text-red5 shadow"
          onClick={handleCancel}
        >
          <img className="size-4" src="/images/icons/icon-error.svg" />
          Cancel
        </Button>
      }
    >
      <div className="p-4 sm:px-8 md:px-16 lg:px-32 xl:px-64">
        <h3 className="mb-2 text-lg font-medium">Generate blog ideas</h3>
        <p className="mb-4 text-sm text-gray-600 sm:text-base">
          Let AI help you come up with blog ideas. Insert what you want to write about. Insert your
          SEO keywords if you want them in your blog.
        </p>

        {ideaResearchData ? (
          <IdeaResearchCard
            ideaResearchData={ideaResearchData}
            toggleIdeaSelection={toggleIdeaSelection}
            selectedIdeas={selectedIdeas}
            primaryAction={
              selectedIdeas.length > 0
                ? {
                    label: ' SAVE BLOGS IDEAS',
                    onClick: handleSave,
                    loading: createIdeaResearch.isLoading,
                  }
                : undefined
            }
            popoverActions={popoverActions}
            onDelete={handleDeleteIdea}
          />
        ) : (
          <QuickIdeasForm defaultValues={quickIdeasFormData} />
        )}
      </div>

      <SelectProjectDialog
        isOpen={isSelectProjectDialogOpen}
        toggleDialog={toggleSelectProjectDialog}
        onSave={handleSave}
      />
    </DashboardContainer>
  );
};

export default QuickIdeas;
