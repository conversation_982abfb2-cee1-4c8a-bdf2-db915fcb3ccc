import { useState, useCallback } from 'react';

export const useIdeaSelection = (initialIdeas: string[] = []) => {
  const [selectedIdeas, setSelectedIdeas] = useState<string[]>(initialIdeas);

  const toggleIdeaSelection = useCallback((idea: string) => {
    setSelectedIdeas((prev) =>
      prev.includes(idea) ? prev.filter((i) => i !== idea) : [...prev, idea]
    );
  }, []);

  const selectAllIdeas = useCallback((ideas: string[]) => {
    setSelectedIdeas(ideas);
  }, []);

  const clearSelection = useCallback(() => {
    setSelectedIdeas([]);
  }, []);

  const isIdeaSelected = useCallback(
    (idea: string) => selectedIdeas.includes(idea),
    [selectedIdeas]
  );

  return {
    selectedIdeas,
    toggleIdeaSelection,
    selectAllIdeas,
    clearSelection,
    isIdeaSelected,
    setSelectedIdeas,
  };
};
