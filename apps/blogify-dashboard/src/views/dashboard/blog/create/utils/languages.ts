export const LANGUAGE_CODES: string[] = [
  'Global English',
  'US English',
  'UK English',
  'Australia English',
  'India English',
  'New Zealand English',
  'Afrikaans',
  'Arabic',
  'Armenian',
  'Azerbaijani',
  'Belarusian',
  'Bosnian',
  'Brazil Portuguese',
  'Bulgarian',
  'Catalan',
  'China Chinese',
  'Chinese',
  'Croatian',
  'Czech',
  'Danish',
  'Dutch',
  'Estonian',
  'Finnish',
  'Flemish',
  'French',
  'French Canada',
  'German',
  'Greek',
  'Hebrew',
  'Hindi',
  'Hindi Latin',
  'Hungarian',
  'Icelandic',
  'Indonesian',
  'Irish',
  'Italian',
  'Japanese',
  'Kannada',
  'Kazakh',
  'Korean',
  'Latin American Spanish',
  'Latvian',
  'Lithuanian',
  'Macedonian',
  'Malay',
  'Maltese',
  'Marathi',
  'Maori',
  'Nepali',
  'Norwegian',
  'Persian',
  'Polish',
  'Portuguese',
  'Romanian',
  'Russian',
  'Serbian',
  'Slovak',
  'Slovenian',
  'Spanish',
  'Swahili',
  'Swedish',
  'Taiwan Chinese',
  'Tamil',
  'Thai',
  'Turkish',
  'Ukrainian',
  'Urdu',
  'Vietnamese',
  'Welsh',
];

export const BLOG_LANGUAGES = [
  {
    value: 'english',
    name: 'English',
    translation: 'English',
    iso: 'en',
  },
  {
    value: 'american english',
    name: 'American English',
    translation: 'English (US)',
    iso: 'en-US',
  },
  {
    value: 'british english',
    name: 'British English',
    translation: 'English (UK)',
    iso: 'en-GB',
  },
  {
    value: 'canadian english',
    name: 'Canadian English',
    translation: 'English (Canadian)',
    iso: 'en-CA',
  },
  {
    value: 'australian english',
    name: 'Australian English',
    translation: 'English (Australian)',
    iso: 'en-AU',
  },
  {
    value: 'new zealand english',
    name: 'New Zealand English',
    translation: 'English (New Zealand)',
    iso: 'en-NZ',
  },
  {
    value: 'south african english',
    name: 'South African English',
    translation: 'English (South African)',
    iso: 'en-ZA',
  },
  {
    value: 'spanish',
    name: 'Spanish',
    translation: 'Español',
    iso: 'es',
  },
  {
    value: 'mandarin',
    name: 'Mandarin Chinese',
    translation: '普通话',
    iso: 'zh-CN',
  },
  {
    value: 'french',
    name: 'French',
    translation: 'Français',
    iso: 'fr',
  },
  {
    value: 'portuguese',
    name: 'Portuguese',
    translation: 'Português',
    iso: 'pt',
  },
  {
    value: 'russian',
    name: 'Russian',
    translation: 'Русский',
    iso: 'ru',
  },
  {
    value: 'japanese',
    name: 'Japanese',
    translation: '日本語',
    iso: 'ja',
  },
  {
    value: 'german',
    name: 'German',
    translation: 'Deutsch',
    iso: 'de',
  },
  {
    value: 'korean',
    name: 'Korean',
    translation: '한국어',
    iso: 'ko',
  },
  {
    value: 'italian',
    name: 'Italian',
    translation: 'Italiano',
    iso: 'it',
  },
  {
    value: 'dutch',
    name: 'Dutch',
    translation: 'Nederlands',
  },
  {
    value: 'afrikaans',
    name: 'Afrikaans',
    translation: 'Afrikaans',
  },
  {
    value: 'albanian',
    name: 'Albanian',
    translation: 'Shqip',
  },
  {
    value: 'amharic',
    name: 'Amharic',
    translation: 'አማርኛ',
  },
  {
    value: 'arabic',
    name: 'Arabic',
    translation: 'العربية',
    iso: 'ar',
  },
  {
    value: 'armenian',
    name: 'Armenian',
    translation: 'Հայերեն',
  },
  {
    value: 'assamese',
    name: 'Assamese',
    translation: 'অসমীয়া',
  },
  {
    value: 'awadhi',
    name: 'Awadhi',
    translation: 'अवधी',
  },
  {
    value: 'azerbaijani',
    name: 'Azerbaijani',
    translation: 'Azərbaycanca',
  },
  {
    value: 'bashkir',
    name: 'Bashkir',
    translation: 'Башҡорт',
  },
  {
    value: 'basque',
    name: 'Basque',
    translation: 'Euskara',
  },
  {
    value: 'belarusian',
    name: 'Belarusian',
    translation: 'Беларуская',
  },
  {
    value: 'bengali',
    name: 'Bengali',
    translation: 'বাংলা',
    iso: 'bn',
  },
  {
    value: 'bhojpuri',
    name: 'Bhojpuri',
    translation: 'भोजपुरी',
  },
  {
    value: 'bosnian',
    name: 'Bosnian',
    translation: 'Bosanski',
  },
  {
    value: 'brazilian-portuguese',
    name: 'Brazilian Portuguese',
    translation: 'português brasileiro',
  },
  {
    value: 'bulgarian',
    name: 'Bulgarian',
    translation: 'български',
  },
  {
    value: 'burmese',
    name: 'Burmese',
    translation: 'မြန်မာစာ',
  },
  {
    value: 'cantonese',
    name: 'Cantonese (Yue)',
    translation: '粵語',
  },
  {
    value: 'catalan',
    name: 'Catalan',
    translation: 'Català',
  },
  {
    value: 'cebuano',
    name: 'Cebuano',
    translation: 'Binisaya',
  },
  {
    value: 'chhattisgarhi',
    name: 'Chhattisgarhi',
    translation: 'छत्तीसगढ़ी',
  },
  {
    value: 'chichewa',
    name: 'Chichewa',
    translation: 'Chichewa',
  },
  {
    value: 'chinese',
    name: 'Chinese',
    translation: '中文',
    iso: 'zh',
  },
  {
    value: 'chinese traditional',
    name: 'Chinese (Traditional)',
    translation: '中文',
  },
  {
    value: 'corsican',
    name: 'Corsican',
    translation: 'Corsu',
  },
  {
    value: 'croatian',
    name: 'Croatian',
    translation: 'Hrvatski',
  },
  {
    value: 'czech',
    name: 'Czech',
    translation: 'Čeština',
  },
  {
    value: 'danish',
    name: 'Danish',
    translation: 'Dansk',
  },
  {
    value: 'dogri',
    name: 'Dogri',
    translation: 'डोगरी',
  },
  {
    value: 'dutch',
    name: 'Dutch',
    translation: 'Nederlands',
  },
  {
    value: 'english',
    name: 'English',
    translation: 'English',
  },
  {
    value: 'esperanto',
    name: 'Esperanto',
    translation: 'Esperanto',
  },
  {
    value: 'estonian',
    name: 'Estonian',
    translation: 'Eesti',
  },
  {
    value: 'faroese',
    name: 'Faroese',
    translation: 'Føroyskt',
  },
  {
    value: 'fijian',
    name: 'Fijian',
    translation: 'Na Vosa Vakaviti',
  },
  {
    value: 'filipino',
    name: 'Filipino',
    translation: 'Filipino',
  },
  {
    value: 'finnish',
    name: 'Finnish',
    translation: 'Suomi',
  },
  {
    value: 'french',
    name: 'French',
    translation: 'Français',
  },
  {
    value: 'frisian',
    name: 'Frisian',
    translation: 'Frysk',
  },
  {
    value: 'galician',
    name: 'Galician',
    translation: 'Galego',
  },
  {
    value: 'georgian',
    name: 'Georgian',
    translation: 'ქართული',
  },
  {
    value: 'german',
    name: 'German',
    translation: 'Deutsch',
  },
  {
    value: 'german informal du-form',
    name: 'German Informal Du-form',
    translation: 'Umgangssprache',
  },
  {
    value: 'greek',
    name: 'Greek',
    translation: 'Ελληνικά',
  },
  {
    value: 'guarani',
    name: 'Guarani',
    translation: 'Avañeẽ',
  },
  {
    value: 'gujarati',
    name: 'Gujarati',
    translation: 'ગુજરાતી',
  },
  {
    value: 'haitian creole',
    name: 'Haitian Creole',
    translation: 'Kreyòl ayisyen',
  },
  {
    value: 'haryanvi',
    name: 'Haryanvi',
    translation: 'हरियाणवी',
  },
  {
    value: 'hausa',
    name: 'Hausa',
    translation: 'Hausa',
  },
  {
    value: 'hawaiian',
    name: 'Hawaiian',
    translation: 'ʻŌlelo Hawaiʻi',
  },
  {
    value: 'hebrew',
    name: 'Hebrew',
    translation: 'עברית',
  },
  {
    value: 'hindi',
    name: 'Hindi',
    translation: 'हिंदी',
    iso: 'hi',
  },
  {
    value: 'hungarian',
    name: 'Hungarian',
    translation: 'Magyar',
  },
  {
    value: 'icelandic',
    name: 'Icelandic',
    translation: 'Íslenska',
  },
  {
    value: 'igbo',
    name: 'Igbo',
    translation: 'Igbo',
  },
  {
    value: 'indonesian',
    name: 'Indonesian',
    translation: 'Bahasa Indonesia',
  },
  {
    value: 'irish',
    name: 'Irish',
    translation: 'Gaeilge',
  },
  {
    value: 'italian',
    name: 'Italian',
    translation: 'Italiano',
  },
  {
    value: 'japanese',
    name: 'Japanese',
    translation: '日本語',
  },
  {
    value: 'javanese',
    name: 'Javanese',
    translation: 'Basa Jawa',
  },
  {
    value: 'kannada',
    name: 'Kannada',
    translation: 'ಕನ್ನಡ',
  },
  {
    value: 'kashmiri',
    name: 'Kashmiri',
    translation: 'कश्मीरी',
  },
  {
    value: 'kazakh',
    name: 'Kazakh',
    translation: 'Қазақша',
  },
  {
    value: 'khmer',
    name: 'Khmer',
    translation: 'ភាសាខ្មែរ',
  },
  {
    value: 'kinyarwanda',
    name: 'Kinyarwanda',
    translation: 'Kinyarwanda',
  },
  {
    value: 'kirundi',
    name: 'Kirundi',
    translation: 'Kirundi',
  },
  {
    value: 'konkani',
    name: 'Konkani',
    translation: 'कोंकणी',
  },
  {
    value: 'korean',
    name: 'Korean',
    translation: '한국어',
  },
  {
    value: 'kurdish',
    name: 'Kurdish',
    translation: 'Kurdî',
  },
  {
    value: 'kyrgyz',
    name: 'Kyrgyz',
    translation: 'Кыргызча',
  },
  {
    value: 'lao',
    name: 'Lao',
    translation: 'ພາສາລາວ',
  },
  {
    value: 'latvian',
    name: 'Latvian',
    translation: 'Latviešu',
  },
  {
    value: 'lithuanian',
    name: 'Lithuanian',
    translation: 'Lietuvių',
  },
  {
    value: 'luxembourgish',
    name: 'Luxembourgish',
    translation: 'Lëtzebuergesch',
  },
  {
    value: 'macedonian',
    name: 'Macedonian',
    translation: 'Македонски',
  },
  {
    value: 'maithili',
    name: 'Maithili',
    translation: 'मैथिली',
  },
  {
    value: 'malagasy',
    name: 'Malagasy',
    translation: 'Malagasy',
  },
  {
    value: 'malay',
    name: 'Malay',
    translation: 'Bahasa Melayu',
  },
  {
    value: 'malayalam',
    name: 'Malayalam',
    translation: 'മലയാളം',
  },
  {
    value: 'maltese',
    name: 'Maltese',
    translation: 'Malti',
  },
  {
    value: 'mandarin',
    name: 'Mandarin Chinese',
    translation: '普通话',
  },
  {
    value: 'maori',
    name: 'Maori',
    translation: 'Te Reo Māori',
  },
  {
    value: 'marathi',
    name: 'Marathi',
    translation: 'मराठी',
  },
  {
    value: 'marwari',
    name: 'Marwari',
    translation: 'मारवाड़ी',
  },
  {
    value: 'meiteilon manipuri',
    name: 'Meiteilon (Manipuri)',
    translation: 'মৈতৈলোন্',
  },
  {
    value: 'min-nan',
    name: 'Min Nan',
    translation: '閩南語',
  },
  {
    value: 'mizo',
    name: 'Mizo',
    translation: 'Mizo ţawng',
  },
  {
    value: 'moldovan',
    name: 'Moldovan',
    translation: 'Moldovenească',
  },
  {
    value: 'mongolian',
    name: 'Mongolian',
    translation: 'Монгол',
  },
  {
    value: 'montenegrin',
    name: 'Montenegrin',
    translation: 'Crnogorski',
  },
  {
    value: 'nahuatl',
    name: 'Nahuatl',
    translation: 'Nāhuatlahtōlli',
  },
  {
    value: 'navajo',
    name: 'Navajo',
    translation: 'Diné bizaad',
  },
  {
    value: 'ndebele',
    name: 'Ndebele',
    translation: 'isiNdebele',
  },
  {
    value: 'nepali',
    name: 'Nepali',
    translation: 'नेपाली',
  },
  {
    value: 'norwegian',
    name: 'Norwegian',
    translation: 'Norsk',
  },
  {
    value: 'odia',
    name: 'Odia (Oriya)',
    translation: 'ଓଡ଼ିଆ',
  },
  {
    value: 'oriya',
    name: 'Oriya',
    translation: 'ଓଡ଼ିଆ',
  },
  {
    value: 'pashto',
    name: 'Pashto',
    translation: 'پښتو',
  },
  {
    value: 'persian',
    name: 'Persian (Farsi)',
    translation: 'فارسی',
  },
  {
    value: 'polish',
    name: 'Polish',
    translation: 'Polski',
  },
  {
    value: 'punjabi',
    name: 'Punjabi',
    translation: 'ਪੰਜਾਬੀ',
  },
  {
    value: 'quechua',
    name: 'Quechua',
    translation: 'Runasimi',
  },
  {
    value: 'rajasthani',
    name: 'Rajasthani',
    translation: 'राजस्थानी',
  },
  {
    value: 'romanian',
    name: 'Romanian',
    translation: 'Română',
  },
  {
    value: 'russian',
    name: 'Russian',
    translation: 'Русский',
  },
  {
    value: 'samoan',
    name: 'Samoan',
    translation: 'Gagana faa Sāmoa',
  },
  {
    value: 'sanskrit',
    name: 'Sanskrit',
    translation: 'संस्कृतम्',
  },
  {
    value: 'santali',
    name: 'Santali',
    translation: 'संताली',
  },
  {
    value: 'scots gaelic',
    name: 'Scots Gaelic',
    translation: 'Gàidhlig',
  },
  {
    value: 'sepedi',
    name: 'Sepedi',
    translation: 'Sesotho sa Leboa',
  },
  {
    value: 'serbian',
    name: 'Serbian',
    translation: 'Српски',
  },
  {
    value: 'sesotho',
    name: 'Sesotho',
    translation: 'Sesotho',
  },
  {
    value: 'shona',
    name: 'Shona',
    translation: 'chiShona',
  },
  {
    value: 'sicilian',
    name: 'Sicilian',
    translation: 'Sicilianu',
  },
  {
    value: 'sindhi',
    name: 'Sindhi',
    translation: 'سنڌي',
  },
  {
    value: 'sinhala',
    name: 'Sinhala',
    translation: 'සිංහල',
  },
  {
    value: 'sinhalese',
    name: 'Sinhalese',
    translation: 'සිංහල',
  },
  {
    value: 'slovak',
    name: 'Slovak',
    translation: 'Slovenčina',
  },
  {
    value: 'slovene',
    name: 'Slovene',
    translation: 'Slovenščina',
  },
  {
    value: 'slovenian',
    name: 'Slovenian',
    translation: 'Slovenščina',
  },
  {
    value: 'somali',
    name: 'Somali',
    translation: 'Soomaali',
  },
  {
    value: 'spanish',
    name: 'Spanish',
    translation: 'Español',
  },
  {
    value: 'sundanese',
    name: 'Sundanese',
    translation: 'Basa Sunda',
  },
  {
    value: 'swahili',
    name: 'Swahili',
    translation: 'Kiswahili',
  },
  {
    value: 'swedish',
    name: 'Swedish',
    translation: 'svɛ̂nːska',
  },
  {
    value: 'tagalog',
    name: 'Tagalog',
    translation: 'Tagalog',
  },
  {
    value: 'tajik',
    name: 'Tajik',
    translation: 'тоҷикӣ',
  },
  {
    value: 'tamil',
    name: 'Tamil',
    translation: 'தமிழ்',
  },
  {
    value: 'tatar',
    name: 'Tatar',
    translation: 'татар теле',
  },
  {
    value: 'telugu',
    name: 'Telugu',
    translation: 'తెలుగు',
  },
  {
    value: 'thai',
    name: 'Thai',
    translation: 'ภาษาไทย',
  },
  {
    value: 'tibetan',
    name: 'Tibetan',
    translation: 'བོད་སྐད་',
  },
  {
    value: 'tsonga',
    name: 'Tsonga',
    translation: 'Xitsonga',
  },
  {
    value: 'turkish',
    name: 'Turkish',
    translation: 'Türkçe',
  },
  {
    value: 'turkmen',
    name: 'Turkmen',
    translation: 'Türkmen',
  },
  {
    value: 'twi',
    name: 'Twi',
    translation: 'Twi',
  },
  {
    value: 'uighur',
    name: 'Uighur',
    translation: 'ئۇيغۇرچە',
  },
  {
    value: 'ukrainian',
    name: 'Ukrainian',
    translation: 'Українська',
  },
  {
    value: 'urdu',
    name: 'Urdu',
    translation: 'اردو',
  },
  {
    value: 'uzbek',
    name: 'Uzbek',
    translation: 'Ўзбек',
  },
  {
    value: 'valencian',
    name: 'Valencian',
    translation: 'Valencià',
  },
  {
    value: 'venda',
    name: 'Venda',
    translation: 'Tshivenḓa',
  },
  {
    value: 'vietnamese',
    name: 'Vietnamese',
    translation: 'Tiếng Việt',
  },
  {
    value: 'welsh',
    name: 'Welsh',
    translation: 'Cymraeg',
  },
  {
    value: 'wolof',
    name: 'Wolof',
    translation: 'Wolof',
  },
  {
    value: 'wu',
    name: 'Wu',
    translation: '吴语',
  },
  {
    value: 'xhosa',
    name: 'Xhosa',
    translation: 'isiXhosa',
  },
  {
    value: 'xitsonga',
    name: 'Xitsonga',
    translation: 'Xitsonga',
  },
  {
    value: 'yiddish',
    name: 'Yiddish',
    translation: 'ייִדיש',
  },
  {
    value: 'yoruba',
    name: 'Yoruba',
    translation: 'Yorùbá',
  },
  {
    value: 'zhuang',
    name: 'Zhuang',
    translation: 'Vahcuengh',
  },
  {
    value: 'zulu',
    name: 'Zulu',
    translation: 'isiZulu',
  },
];
