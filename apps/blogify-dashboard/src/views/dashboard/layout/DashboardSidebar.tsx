import {
  MdOutlineFeatured<PERSON>layList,
  MdEditDocument,
  MdDashboard,
  MdExtension,
  MdSettings,
  MdPayment,
  MdWallet,
  MdPhoto,
} from 'react-icons/md';
import { FaChildReaching, FaAngleRight } from 'react-icons/fa6';
import { FaYoutubeSquare, FaLink } from 'react-icons/fa';
import { TbAffiliateFilled } from 'react-icons/tb';
import { LuLibraryBig } from 'react-icons/lu';
import { HiUserGroup } from 'react-icons/hi2';
import { VscGlobe } from 'react-icons/vsc';
import { useState } from 'react';

import { getPackageAndPeriod } from '@/utils';
import { useStoreState } from '@/store';
import { cn } from '@ps/ui/lib/utils';
import Link from '@/components/common/Link';

const DASHBOARD_MENU_CONFIG = [
  { name: 'Overview', domId: 'overview', link: '.', icon: <MdDashboard size={20} /> },
  { name: 'My Blogs', domId: 'blogs', link: './blogs', icon: <MdEditDocument size={20} /> },
  { name: 'My Blog Sites', domId: 'websites', link: './websites', icon: <VscGlobe size={20} /> },
  {
    name: 'Affiliate Dashboard',
    domId: 'affiliate',
    link: './affiliate',
    icon: <TbAffiliateFilled size={20} />,
  },
  { name: 'My Wallet', domId: '#wallet', link: './wallet', icon: <MdWallet size={20} /> },
];

const DASHBOARD_LIBRARY_GROUP = [
  {
    name: 'Image Library',
    domId: 'images',
    link: './images',
    icon: <MdPhoto size={20} className="hidden" />,
  },
  {
    name: 'Affiliate Link Library',
    domId: 'link-library',
    link: './link-library',
    icon: <FaLink size={20} className="hidden" />,
  },
];

const DASHBOARD_SETTINGS_GROUP = [
  {
    name: 'Profile',
    domId: 'settings',
    link: './settings',
    icon: <MdSettings size={20} className="hidden" />,
  },
  {
    name: 'Users',
    domId: 'user-settings',
    link: './users',
    icon: <HiUserGroup size={20} className="hidden" />,
  },
  {
    name: 'Subscription',
    domId: 'subscription',
    link: './subscription',
    icon: <MdPayment size={20} className="hidden" />,
  },
];

const DASHBOARD_ADDONS_MENU_CONFIG = [
  {
    name: 'YouTube Connect',
    domId: 'channels',
    link: './youtube',
    icon: <FaYoutubeSquare size={20} />,
  },
  {
    name: 'Writing Snippets',
    domId: 'writingSnippets',
    link: './writing-snippets/all',
    icon: <MdEditDocument size={20} />,
  },
  {
    name: 'Addons',
    domId: 'addons',
    link: './addons',
    icon: <MdExtension size={20} />,
  },
];

const DashboardSidebar = ({
  isSidebarVisible,
  toggleSidebar,
}: {
  isSidebarVisible: boolean;
  toggleSidebar: () => void;
}) => {
  const user = useStoreState((u) => u.user.current);

  DASHBOARD_ADDONS_MENU_CONFIG[0].name = user.hasYouTubeProAddon
    ? 'YouTube Connect Pro'
    : 'YouTube Connect';

  const [libraryOpen, setLibraryOpen] = useState(false);
  const [settingsOpen, setSettingsOpen] = useState(false);

  const toggleLibrary = () => setLibraryOpen((v) => !v);
  const toggleSettings = () => setSettingsOpen((v) => !v);

  // Create menu config with conditional Blog Ideas item
  const menuConfig = [...DASHBOARD_MENU_CONFIG];
  if (user?.email?.endsWith('@blogify.ai') && menuConfig.every((m) => m.name !== 'Blog Ideas')) {
    menuConfig.splice(1, 0, {
      name: 'Blog Ideas',
      domId: 'blog-ideas',
      link: './blog-ideas',
      icon: <FaChildReaching size={20} />,
    });
  }

  return (
    <div className="font-inter text-black4">
      <aside
        // eslint-disable-next-line tailwindcss/no-custom-classname
        className={cn(
          'sidebar sticky top-0 z-50 h-screen w-64 overflow-y-auto px-3 py-2.5 transition-transform duration-200 ease-in-out',
          'max-lg:fixed max-lg:h-[calc(100vh-0px)] max-lg:translate-x-[-80vw] max-lg:bg-bg2 [&_a.active_svg]:text-primary',
          '[&_a.active]:rounded-md [&_a.active]:bg-white [&_a.active]:text-primary [&_a.active]:shadow-sm',
          { 'max-lg:translate-x-0': isSidebarVisible }
        )}
      >
        <div className="flex flex-row items-center justify-start gap-3 pb-8">
          <a href="/">
            <img src="/images/blogify.svg" height={40} alt="Blogify.ai Logo" />
          </a>
          <div>
            <h2 className="text-md font-semibold leading-[18px]">Blogify.ai</h2>
            <h4 className="text-sm font-normal capitalize leading-4 text-gray9">
              {getPackageAndPeriod(user.subscriptionPlan).plan}
            </h4>
          </div>
        </div>

        {menuConfig.map((config, i) => (
          <DashboardSidebarItem key={i} {...config} onClick={toggleSidebar} />
        ))}

        {/* Library Group */}
        <div className="mt-3">
          <div
            className="cursor-pointer select-none rounded-md px-2.5 hover:bg-bg2"
            onClick={toggleLibrary}
          >
            <div className="flex cursor-pointer select-none items-center gap-3 font-medium">
              <LuLibraryBig size={20} className="text-gray9" />
              <span className="hover:text-primary">Libraries</span>
              <span
                style={{
                  marginLeft: 'auto',
                  transform: libraryOpen ? 'rotate(90deg)' : 'rotate(0deg)',
                  transition: 'transform 0.3s',
                }}
              >
                <FaAngleRight size={12} />
              </span>
            </div>
          </div>
          {libraryOpen && (
            <div className="mt-1 flex gap-2 pl-2.5">
              <div className="ml-2 w-0.5 rounded-sm bg-gray10" />
              <div className="grow text-sm">
                {DASHBOARD_LIBRARY_GROUP.map((config, i) => (
                  <DashboardSidebarItem key={i} {...config} onClick={toggleSidebar} small />
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Settings Group */}
        <div className="mt-3">
          <div
            className="cursor-pointer select-none rounded-md px-2.5 hover:bg-bg2"
            onClick={toggleSettings}
          >
            <div className="flex cursor-pointer select-none items-center gap-3 font-medium">
              <MdSettings size={20} className="text-gray9" />
              <span className="hover:text-primary">Settings</span>
              <span
                style={{
                  marginLeft: 'auto',
                  transform: settingsOpen ? 'rotate(90deg)' : 'rotate(0deg)',
                  transition: 'transform 0.3s',
                }}
              >
                <FaAngleRight size={12} />
              </span>
            </div>
          </div>
          {settingsOpen && (
            <div className="mt-1 flex gap-2 pl-2.5">
              <div className="ml-2 w-0.5 rounded-sm bg-gray10" />
              <div className="grow text-sm">
                {DASHBOARD_SETTINGS_GROUP.map((config, i) => (
                  <DashboardSidebarItem key={i} {...config} onClick={toggleSidebar} small />
                ))}
              </div>
            </div>
          )}
        </div>

        <div className="pb-2 pt-5 text-xs font-semibold uppercase text-gray9">Addons</div>
        {DASHBOARD_ADDONS_MENU_CONFIG.map((config, i) => (
          <DashboardSidebarItem key={i} {...config} onClick={toggleSidebar} />
        ))}

        <div className="mt-10 flex border-t border-gray11">
          <DashboardSidebarItem
            {...{
              name: 'Upcoming Features',
              link: './upcoming-features',
              icon: <MdOutlineFeaturedPlayList size={20} />,
            }}
            onClick={toggleSidebar}
          />
        </div>
      </aside>

      {isSidebarVisible && (
        <div
          className="fixed inset-0 z-[49] hidden bg-black/60 max-lg:block"
          onClick={toggleSidebar}
        />
      )}
    </div>
  );
};

const DashboardSidebarItem = ({
  name,
  link,
  icon,
  domId,
  onClick,
  small = false,
}: {
  name: string;
  link: string;
  icon: React.ReactNode;
  domId?: string;
  onClick: () => void;
  small?: boolean;
}) => (
  <div className="mb-1 rounded-md hover:bg-gray11">
    <Link
      to={link}
      onClick={onClick}
      id={domId}
      end
      className={cn(
        'flex w-full items-center gap-3 px-2.5 py-2 font-medium text-black hover:text-blue [&_svg]:text-gray9 hover:[&_svg]:text-blue',
        small ? 'text-sm' : 'text-base'
      )}
    >
      {icon}
      <div className="leading-5">{name}</div>
    </Link>
  </div>
);

export default DashboardSidebar;
