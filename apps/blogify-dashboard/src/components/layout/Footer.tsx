import { cn } from '@ps/ui/lib/utils';

type LinkMeta = { name: string; href: string };

const terms: LinkMeta[] = [
  { name: 'Privacy Policy', href: '/privacy' },
  { name: 'Terms & Conditions', href: '/terms' },
];

const navigation: LinkMeta[] = [
  { name: 'Pricing', href: '/#pricing-table' },
  { name: 'Reviews', href: '/#user-reviews' },
  { name: 'F.A.Q.', href: '/faq' },
  { name: 'Blogs', href: '/blog' },
  { name: 'Partner Program', href: '/partner-program' },
];

const social: LinkMeta[] = [
  { name: 'Facebook', href: 'https://www.facebook.com/Blogifyai' },
  { name: 'X / Twitter', href: 'https://twitter.com/blogify_ai' },
  { name: 'LinkedIn', href: 'https://www.linkedin.com/company/blogifyai' },
  { name: '<PERSON> Hunt', href: 'https://www.producthunt.com/products/blogify' },
  { name: 'Pinterest', href: 'https://www.pinterest.com/blogifyai' },
  { name: 'Instagram', href: 'https://www.instagram.com/blogifyaiinc' },
];
const comparison: LinkMeta[] = [
  { name: 'Copy.ai Alternative', href: '/blogify-vs-copy-ai' },
  { name: 'Rytr Alternative', href: '/blogify-vs-rytr' },
  { name: 'WriteSonic Alternative', href: '/blogify-vs-writesonic' },
  { name: 'Jasper ai Alternative', href: '/blogify-vs-jasperai/' },
  { name: 'Type ai Alternative', href: '/blogify-vs-typeai' },
  { name: 'Yaarai Alternative', href: '/blogify-vs-yaarai' },
  { name: 'Satellitor Alternative', href: '/blogify-vs-satellitor' },
  { name: 'Opendraft Alternative', href: '/blogify-vs-opendraft' },
  { name: 'WriteSeed Alternative', href: '/blogify-vs-writeseed' },
  { name: 'Aiseo Alternative', href: '/blogify-vs-aiseo' },
  { name: 'Frase Alternative', href: '/blogify-vs-frase' },
  { name: 'WriteCream Alternative', href: '/blogify-vs-writecream' },
  { name: 'ContentBot Alternative', href: '/blogify-vs-contentbot' },
  { name: 'Scalenut Alternative', href: '/blogify-vs-scalenut' },
];
const howTo: LinkMeta[] = [
  { name: 'Video to Blog', href: '/how-to-turn-your-video-into-a-blog' },
  { name: 'Audio to Blog', href: '/how-to-turn-your-audio-into-a-blog' },
  { name: 'Text Prompt to Blog', href: '/how-to-create-a-blog-from-a-text-prompt' },
  { name: 'Webpage to Blog', href: '/how-to-create-a-blog-from-a-web-url' },
  {
    name: 'YouTube Connect to Blog',
    href: '/how-to-use-the-youtube-connect-feature-to-create-blogs',
  },
  { name: 'Writing Snippets', href: '/how-to-create-writing-snippets' },
];

const howToVideo: LinkMeta[] = [
  { name: 'YouTube Video to Blog', href: '/how***************************************' },
  { name: 'Vimeo Video to Blog', href: '/how-to-turn-your-vimeo-video-into-a-blog' },
  { name: 'Rumble Video to Blog', href: '/how-to-turn-your-rumble-video-into-a-blog' },
  { name: 'TikTok Video to Blog', href: '/how-to-turn-your-tiktok-video-into-a-blog' },
  { name: 'Facebook Video to Blog', href: '/how-to-turn-your-facebook-video-into-a-blog' },
  { name: 'Twitter to Blog', href: '/how-to-turn-your-twitter-into-a-blog' },
  { name: 'Daily Motion to Blog', href: '/how-to-turn-your-daily-motion-into-a-blog' },
  { name: 'TED to Blog', href: '/how-to-turn-your-ted-into-a-blog' },
];

const howToAudio: LinkMeta[] = [
  { name: 'Apple Podcast to Blog', href: '/how-to-turn-your-apple-podcast-into-a-blog' },
  { name: 'Castbox to Blog', href: '/how-to-turn-your-castbox-into-a-blog' },
  { name: 'Podchaser to Blog', href: '/how-to-turn-your-podchaser-into-a-blog' },
  { name: 'Podbean to Blog', href: '/how-to-turn-your-Podbean-into-a-blog' },
  { name: 'Podcast Addict to Blog', href: '/how-to-turn-your-podcast-addict-into-a-blog' },
  { name: 'SoundCloud to Blog', href: '/how-to-turn-your-soundcloud-into-a-blog' },
  { name: 'iHeartRadio to Blog', href: '/how-to-turn-your-iheartradio-into-a-blog' },
  { name: 'TuneIn to Blog', href: '/how-to-turn-your-tunein-into-a-blog' },
];

const howToWebpage: LinkMeta[] = [
  { name: 'Blogger to Blog', href: '/how-to-turn-your-blogger-into-a-blog' },
  { name: 'BBC to Blog', href: '/how-to-turn-your-bbc-into-a-blog' },
  { name: 'Verge to Blog', href: '/how-to-turn-your-verge-into-a-blog' },
  { name: 'Apple Article to Blog', href: '/how-to-turn-your-apple-article-into-a-blog' },
  { name: 'Reddit to Blog', href: '/how-to-turn-your-reddit-into-a-blog' },
  { name: 'Quora to Blog', href: '/how-to-turn-your-quora-into-a-blog' },
  { name: 'Stack Overflow to Blog', href: '/how-to-turn-your-stackoverflow-into-a-blog' },
  { name: 'XDA Forum to Blog', href: '/how-to-turn-your-xdaforum-into-a-blog' },
];

const howToECommerce: LinkMeta[] = [
  { name: 'Amazon to Blog', href: '/how-to-turn-your-amazon-into-a-blog' },
  { name: 'Shopify to Blog', href: '/how-to-turn-your-shopify-into-a-blog' },
  { name: 'Etsy to Blog', href: '/how-to-turn-your-esty-into-a-blog' },
  { name: 'eBay to Blog', href: '/how-to-turn-your-ebay-into-a-blog' },
  { name: 'IKEA to Blog', href: '/how-to-turn-your-ikea-into-a-blog' },
  { name: 'Nike to Blog', href: '/how-to-turn-your-nike-into-a-blog' },
  { name: 'Flipkart to Blog', href: '/how-to-turn-your-flipkart-into-a-blog' },
  { name: 'Walmart to Blog', href: '/how-to-turn-your-walmart-into-a-blog' },
];
const integration: LinkMeta[] = [
  { name: 'Wordpress.com Integration', href: 'https://youtu.be/-yNsSg9UwNQ' },
  { name: 'Wordpress.org Integration', href: 'https://youtu.be/OG8ZAvY7uKU' },
  { name: 'Medium.com Integration', href: 'https://youtu.be/6l40mYiPgGY' },
  { name: 'Blogger.com Integration', href: 'https://youtu.be/2YLxsOkRuH0' },
  { name: 'LinkedIn.com Integration', href: 'https://youtu.be/Ps9bdunOV9c' },
  { name: 'Twitter.com Integration', href: 'https://youtu.be/w3U_mBFkkDc' },
  { name: 'Facebook.com Integration', href: 'https://youtu.be/9hWI1X_cqHM' },
  { name: 'Mailchimp.com Integration', href: 'https://youtu.be/NWgFu7Sz1G8' },
  { name: 'Zapier.com Integration', href: 'https://youtu.be/N_ItZBHbuPw' },
];

const LINKS: { title: string; links: LinkMeta[] }[] = [
  { title: 'Blogify', links: navigation },
  { title: 'Social', links: social },
  { title: 'Product Comparison', links: comparison },
  { title: 'How To Generate Blog', links: howTo },
  { title: 'How To Generate Video to Blog', links: howToVideo },
  { title: 'How To Generate Audio to Blog', links: howToAudio },
  { title: 'How To Generate Webpage to Blog', links: howToWebpage },
  { title: 'How To Generate E-Commerce to Blog', links: howToECommerce },

  { title: 'Integration', links: integration },
];

export default function Footer() {
  return (
    <footer className="mx-auto max-w-7xl p-6 pb-20 pt-0 text-typo-secondary">
      <img
        className="w-full py-14"
        src="/images/blogify-logo-faded.svg"
        alt="Blogify Logo Footer"
      />

      <div className="flex flex-col justify-between gap-5 pt-1 sm:flex-row lg:gap-x-12">
        <div className="sm:w-1/3 md:w-2/5 lg:w-1/5">
          <a href="/">
            <img src="/images/blogify-logo-light.svg" alt="Blogify Brand Logo Light" />
          </a>
          <p className="mb-10 mt-3 text-sm">&copy; 2023 PixelShadow Inc. All rights reserved.</p>

          {terms.map((item) => (
            <a
              className="mt-3 block text-sm font-medium hover:text-typo-secondary/75 hover:underline"
              target={item.href.startsWith('http') ? '_blank' : '_self'}
              href={item.href}
              key={item.name}
            >
              {item.name}
            </a>
          ))}
        </div>

        <div className="sm:w-2/3 md:w-3/5 lg:w-4/5">
          <div className="grid grid-cols-1 gap-x-5 gap-y-12 sm:grid-cols-2 lg:grid-cols-4 lg:gap-x-12 lg:gap-y-20">
            {LINKS.map((l, i) => (
              <LinkGroup key={i} {...l} />
            ))}
          </div>
        </div>
      </div>
    </footer>
  );
}

const LinkGroup = ({
  title,
  links,
  className,
}: {
  title: string;
  links: LinkMeta[];
  className?: string;
}) => (
  <section className={cn('flex shrink-0 flex-col gap-3', className)}>
    <h4 className="text-base font-bold">{title}</h4>
    <ul className="flex flex-col gap-3">
      {links.map((item, index) => (
        <li key={index}>
          <a
            key={item.name}
            href={item.href}
            target={item.href.startsWith('http') ? '_blank' : '_self'}
            className="text-sm font-medium hover:text-typo-secondary/75 hover:underline"
          >
            {item.name}
          </a>
        </li>
      ))}
    </ul>
  </section>
);
