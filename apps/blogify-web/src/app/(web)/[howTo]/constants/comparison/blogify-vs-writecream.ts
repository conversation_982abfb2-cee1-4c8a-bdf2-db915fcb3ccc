import type { ComparePageData, ComparisonFeature, ComparisonItem } from '../../types';
import Blogify<PERSON>ogo from '@img/Blogify_logo_white.svg';
import WritePost from '@img/page-assets/comparison/writepost.png';
import Blogifypost from '@img/page-assets/comparison/blogify-blog-post.png';
import WriteLogo from '@img/page-assets/comparison/writelogo.png';

const featureComparison: ComparisonFeature[] = [
  {
    description: [
      'Create Blog From Anything',
      'Cold Email Personalization',
      'Source File Size',
      'Source File Duration',
      'Pricing Structure',
    ],
    comparisons: {
      blogify: ['green-check', 'red-cross', 'green-check', 'green-check', 'green-check'],
      competitor: ['red-cross', 'green-check', 'red-cross', 'red-cross', 'green-check'],
    },
  },
  {
    description: [
      'Auto-Pilot Blog Creation',
      'Co-Pilot Blog Creation',
      'Voiceover Generation',
      'SEO Score & Optimization',
      'Word Count',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'red-cross', 'green-check', 'green-check'],
      competitor: ['red-cross', 'red-cross', 'green-check', 'green-check', 'green-check'],
    },
  },
  {
    description: [
      'Language Support In Blogs',
      'YouTube Video Voiceovers',
      'Publish on Your Own Site through Domain or Sub-Domain with DNS settings',
      'Schedule Blog Post',
      'Affiliate Monetization with AI',
    ],
    comparisons: {
      blogify: ['green-check', 'red-cross', 'green-check', 'green-check', 'green-check'],
      competitor: ['green-check', 'green-check', 'red-cross', 'red-cross', 'red-cross'],
    },
  },
  {
    description: [
      'Image Generation',
      'Analytics',
      'Writing Snippet (Addon)',
      'YouTube Connect (Addon)',
      'YouTube Connect Pro (Addon)',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'green-check', 'green-check', 'green-check'],
      competitor: ['green-check', 'red-cross', 'green-check', 'red-cross', 'red-cross'],
    },
  },
  {
    description: [
      'Plagiarism Checker',
      'Round the clock e-mail & chat support',
      'Repurpose from other content (Video, Audio, Doc, PDF, Webpage, Ecommerce Page)',
      'Content Rewriting and Editing',
      'Automate & manual blog creation',
    ],
    comparisons: {
      blogify: ['red-cross', 'green-check', 'green-check', 'green-check', 'green-check'],
      competitor: ['green-check', 'green-check', 'red-cross', 'green-check', 'red-cross'],
    },
  },
  {
    description: [
      'Embed blog source',
      'Tone Customization',
      'Blog templates',
      'AI generated cover & content image',
      'User Interface and Experience',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'green-check', 'green-check', 'green-check'],
      competitor: ['red-cross', 'green-check', 'green-check', 'green-check', 'green-check'],
    },
  },
  {
    description: ['AI generated graphs & charts'],
    comparisons: {
      blogify: ['green-check'],
      competitor: ['red-cross'],
    },
  },
];

const comparisonData: ComparisonItem[] = [
  {
    blogify: {
      description:
        "Allows you to generate full blog posts from videos, audio, PDFs, docs, URLs, and more. It's a true content repurposing platform for bloggers.",
    },
    competitor: {
      description:
        'Primarily generates content based on user-provided text prompts; lacks the ability to process diverse content formats directly.',
    },
  },
  {
    blogify: {
      description:
        'Does not offer tools for generating personalized cold emails; focuses primarily on blog content creation.',
    },
    competitor: {
      description:
        'Specializes in creating personalized cold emails and LinkedIn outreach messages, automating tailored messages to individual recipients, enhancing engagement rates for sales and marketing campaigns.',
    },
  },
  {
    blogify: {
      description:
        'Supports large files like lengthy videos or high-resolution documents for blog conversion.',
    },
    competitor: {
      description:
        'Does not support file uploads; content creation is based solely on textual input.',
    },
  },
  {
    blogify: {
      description:
        'Can process long video or audio files to extract and convert their content into structured blog articles.',
    },
    competitor: {
      description: 'No functionality to process audio or video files for content creation.',
    },
  },
  {
    blogify: {
      description:
        'Offers various pricing plans tailored to different user needs; specific details are available on their website.',
    },
    competitor: {
      description:
        'Operates on a freemium model with limited features available for free and additional functionalities unlocked through paid plans.',
    },
  },
  {
    blogify: {
      description:
        'Offers a one-click blog generation feature that automatically creates a full, SEO-ready blog from any content source.',
    },
    competitor: {
      description:
        'Requires manual input and selection at each step; lacks a fully automated content creation mode.',
    },
  },
  {
    blogify: {
      description:
        'Provides assisted editing with section-wise blog suggestions, structure customization, and guided creation options.',
    },
    competitor: {
      description:
        'Does not offer an interactive co-creation mode; users follow a linear content generation process.',
    },
  },
  {
    blogify: {
      description:
        'Lacks voiceover generation capabilities; users need to use external tools for audio content.',
    },
    competitor: {
      description:
        'Provides AI-generated voiceovers in multiple languages and accents, allowing users to create audio content for podcasts, videos, and presentations directly within the platform.',
    },
  },
  {
    blogify: {
      description:
        'Built-in SEO tools provide scoring, optimization suggestions, and keyword guidance directly inside the editor.',
    },
    competitor: {
      description:
        'Offers basic SEO optimization features, such as keyword integration and meta descriptions.',
    },
  },
  {
    blogify: {
      description:
        'Can create complete blog posts ranging from short reads to long-form content automatically.',
    },
    competitor: {
      description:
        'Enables generation of long-form content, with capabilities to produce articles exceeding 1,000 words.',
    },
  },
  {
    blogify: {
      description:
        'Supports over 150 global languages for blog creation, localization, and translation.',
    },
    competitor: {
      description:
        'Offers multilingual support across 75+ languages for diverse content generation.',
    },
  },
  {
    blogify: {
      description:
        'Does not support generating voiceovers for YouTube videos; focuses on text-based content.',
    },
    competitor: {
      description:
        'Enables users to generate voiceovers for YouTube videos, streamlining the content creation process for video marketers and content creators.',
    },
  },
  {
    blogify: {
      description:
        'Enables publishing directly to your own domain or subdomain via DNS configuration.',
    },
    competitor: {
      description:
        'Does not provide direct publishing capabilities to custom domains; content must be manually transferred.',
    },
  },
  {
    blogify: {
      description:
        'Includes scheduling tools to plan and automate blog post publication at specific times.',
    },
    competitor: {
      description: 'Lacks a scheduling feature; users need to manually publish content.',
    },
  },
  {
    blogify: {
      description:
        'Helps you monetize blogs with affiliate link suggestions, product embedding, and earning integrations.',
    },
    competitor: {
      description: 'Does not offer built-in affiliate monetization features.',
    },
  },
  {
    blogify: {
      description:
        'Offers AI-generated images for blog covers and content, enhancing visual appeal.',
    },
    competitor: {
      description:
        'Includes an AI image generator that creates visuals based on textual descriptions, aiding in the creation of cohesive multimedia content.',
    },
  },
  {
    blogify: {
      description:
        'Tracks post performance with built-in analytics tools like views, engagement, and SEO rankings.',
    },
    competitor: {
      description:
        'Does not include analytics functionalities; external tools are required for performance tracking.',
    },
  },
  {
    blogify: {
      description:
        'Allows quick generation of intros, CTAs, and summary sections tailored for blog formats.',
    },
    competitor: {
      description:
        'Includes tools for creating short-form content snippets, such as social media posts and email introductions.',
    },
  },
  {
    blogify: {
      description:
        'Extracts content directly from YouTube videos and turns them into well-structured blog posts.',
    },
    competitor: {
      description:
        'Does not support direct YouTube integration; content must be manually inputted.',
    },
  },
  {
    blogify: {
      description:
        'Enhances YouTube conversion with timestamp extraction, metadata analysis, and video SEO tools.',
    },
    competitor: {
      description: 'Lacks advanced YouTube integration features.',
    },
  },
  {
    blogify: {
      description:
        'Does not have a built-in plagiarism checking tool; users must use external services to ensure content originality.',
    },
    competitor: {
      description:
        'Features an integrated plagiarism checker that scans content for originality, helping users maintain unique and authentic writing.',
    },
  },
  {
    blogify: {
      description:
        'Offers dedicated email and live chat support to assist with content creation, onboarding, and technical queries.',
    },
    competitor: {
      description: 'Provides round-the-clock support via email and chat for user inquiries.',
    },
  },
  {
    blogify: {
      description:
        'Converts various content types into blog posts. Transforms content from multiple sources including videos, audio files, documents, web pages, and ecommerce listings.',
    },
    competitor: {
      description:
        'Limited to text-based input; does not support direct repurposing from diverse content formats.',
    },
  },
  {
    blogify: {
      description:
        'Encourages content rewriting and enhancement for originality but lacks advanced tools for rewriting or editing existing text.',
    },
    competitor: {
      description:
        'Equipped with robust editing tools, including options to expand, shorten, rephrase, and improve content, facilitating comprehensive content refinement.',
    },
  },
  {
    blogify: {
      description:
        'Gives users the option to auto-generate full blogs or manually edit and customize every section.',
    },
    competitor: {
      description:
        'Primarily manual content creation; lacks automation features for blog generation.',
    },
  },
  {
    blogify: {
      description:
        'Enables embedding the original source of the blog (e.g., YouTube or webpage) directly in the post.',
    },
    competitor: {
      description:
        'Does not support embedding of original content sources; users need to manually add references.',
    },
  },
  {
    blogify: {
      description:
        'Allows users to select different tones for content, such as formal or casual, to match the intended audience.',
    },
    competitor: {
      description:
        'Provides over 20 tone options, enabling nuanced control over the style and emotional impact of the content.',
    },
  },
  {
    blogify: {
      description:
        'Offers multiple pre-designed blog layouts for quick formatting and publishing consistency.',
    },
    competitor: {
      description: 'Offers multiple templates tailored for different content types and tones.',
    },
  },
  {
    blogify: {
      description:
        'Automatically generates featured images and content visuals based on blog topics.',
    },
    competitor: {
      description: 'Includes features for creating AI-generated images to accompany content.',
    },
  },
  {
    blogify: {
      description:
        'Features a task-oriented dashboard focused on automated blog generation, ideal for marketers and creators who want minimal friction.',
    },
    competitor: {
      description:
        'Features a user-friendly interface with straightforward navigation, suitable for users with varying levels of technical expertise.',
    },
  },
  {
    blogify: {
      description:
        'Creates visuals like charts, graphs, and data representations directly within your blog post.',
    },
    competitor: {
      description:
        'Does not offer functionalities for generating graphs or charts; external tools are required.',
    },
  },
];

const BLOGIFY_VS_WRITECREAM: ComparePageData = {
  metadata: {
    title: 'Blogify vs Writecream: Detailed Feature Comparison',
    description:
      'Complete comparison of Blogify and Writecream features, pricing, and capabilities for AI-powered content creation in 2025.',
    keywords: ['blogify vs writecream', 'ai writing tools comparison', 'writecream alternative'],
  },
  intro: {
    comparisonTitle: 'Comparison',
    mainTitle: 'Writecream Alternative',
    description:
      'Blogify.ai outshines Writecream by transforming videos, audio files, documents, and URLs into SEO-optimized, ready-to-publish blog posts. While Writecream is known for cold emails and voiceovers, Blogify is purpose-built for full-funnel blog creation — turning any content source into polished articles automatically. 🚀',
    images: {
      competitorImage: WritePost, // Replace with actual Writecream image if available
      blogifyImage: Blogifypost,
    },
  },
  title: 'Unique Blogify Features vs Writecream',
  description:
    'Blogify.ai stands apart from Writecream with its ability to create blogs from diverse content formats, embed SEO strategies, and publish under your custom domain. Whether you’re a marketer, creator, or agency — Blogify streamlines blog creation at scale.',
  comparisonData,
  featureComparison,
  initialVisibleItems: 5,
  images: {
    blogifyLogo: BlogifyLogo,
    competitorLogo: WriteLogo, // Replace with actual Writecream logo if available
  },
};

export default BLOGIFY_VS_WRITECREAM;
