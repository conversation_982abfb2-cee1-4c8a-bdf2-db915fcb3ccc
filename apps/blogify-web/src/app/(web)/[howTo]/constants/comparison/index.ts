import { ComparePageData } from '../../types';
import BL<PERSON><PERSON>FY_VS_AISEO from './blogify-vs-aiseo';
import BLOGIFY_VS_CONTENTBOT from './blogify-vs-contentbot';
import BLOGIFY_VS_COPYAI from './blogify-vs-copy-ai';
import BL<PERSON><PERSON>FY_VS_FRASE from './blogify-vs-frase';
import BLOGIFY_VS_JASPERAI from './blogify-vs-jasperai';
import BLOGIFY_VS_OPENDRAFT from './blogify-vs-opendraft';
import BLOGIFY_VS_RYTR from './blogify-vs-rytr';
import BLO<PERSON>FY_VS_SATELLITOR from './blogify-vs-satellitor';
import BLOGIFY_VS_SCALENUT from './blogify-vs-scalenut';
import BLOGIFY_VS_TYPEAI from './blogify-vs-typeai';
import BLOGIFY_VS_WRITECREAM from './blogify-vs-writecream';
import B<PERSON><PERSON><PERSON><PERSON>Y_VS_WRITESEED from './blogify-vs-writeseed';
import BL<PERSON><PERSON>FY_VS_WRITESONIC from './blogify-vs-writesonic';
import BLOGIFY_VS_YAARAAI from './blogify-vs-yaarai';

const COMPARISON_PAGE_DATA: Record<string, ComparePageData> = {
  'blogify-vs-copy-ai': BLOGIFY_VS_COPYAI,
  'blogify-vs-jasperai': BLOGIFY_VS_JASPERAI,
  'blogify-vs-writesonic': BLOGIFY_VS_WRITESONIC,
  'blogify-vs-rytr': BLOGIFY_VS_RYTR,
  'blogify-vs-typeai': BLOGIFY_VS_TYPEAI,
  'blogify-vs-yaarai': BLOGIFY_VS_YAARAAI,
  'blogify-vs-satellitor': BLOGIFY_VS_SATELLITOR,
  'blogify-vs-opendraft': BLOGIFY_VS_OPENDRAFT,
  'blogify-vs-writeseed': BLOGIFY_VS_WRITESEED,
  'blogify-vs-aiseo': BLOGIFY_VS_AISEO,
  'blogify-vs-frase': BLOGIFY_VS_FRASE,
  'blogify-vs-writecream': BLOGIFY_VS_WRITECREAM,
  'blogify-vs-contentbot': BLOGIFY_VS_CONTENTBOT,
  'blogify-vs-scalenut':BLOGIFY_VS_SCALENUT,
  // Add more comparisons as needed
};

export default COMPARISON_PAGE_DATA;
