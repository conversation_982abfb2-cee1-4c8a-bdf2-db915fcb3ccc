import type { ComparePageData, ComparisonFeature, ComparisonItem } from '../../types';
import Blogify<PERSON>ogo from '@img/Blogify_logo_white.svg';
import FrasePost from '@img/page-assets/comparison/frasepost.png';
import Blogifypost from '@img/page-assets/comparison/blogify-blog-post.png';
import FraseLogo from '@img/page-assets/comparison/fraselogo.png';

const featureComparison: ComparisonFeature[] = [
  {
    description: [
      'Create Blog From Anything',
      'Content Personalization',
      'Source File Size',
      'Source File Duration',
      'Auto-Pilot Blog Creation',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'green-check', 'green-check', 'green-check'],
      competitor: ['red-cross', 'green-check', 'red-cross', 'red-cross', 'red-cross'],
    },
  },
  {
    description: [
      'AI Content Generation Speed',
      'Co-Pilot Blog Creation',
      'SEO Score & Optimization',
      'Word Count',
      'Language Support In Blogs',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'green-check', 'green-check', 'green-check'],
      competitor: ['green-check', 'red-cross', 'green-check', 'green-check', 'red-cross'],
    },
  },
  {
    description: [
      'Publish on Your Own Site through Domain or Sub-Domain with DNS settings',
      'Content Quality Control',
      'Schedule Blog Post',
      'Affiliate Monetization with AI',
      'Analytics',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'green-check', 'green-check', 'green-check'],
      competitor: ['red-cross', 'green-check', 'red-cross', 'red-cross', 'green-check'],
    },
  },
  {
    description: [
      'API Access',
      'Writing Snippet (Addon)',
      'YouTube Connect (Addon)',
      'YouTube Connect Pro (Addon)',
      'Round the clock e-mail & chat support',
    ],
    comparisons: {
      blogify: ['red-cross', 'green-check', 'green-check', 'green-check', 'green-check'],
      competitor: ['green-check', 'red-cross', 'red-cross', 'red-cross', 'red-cross'],
    },
  },
  {
    description: [
      'Repurpose from other content (Video, Audio, Doc, PDF, Webpage, Ecommerce Page)',
      'Content Formats Supported',
      'Automate & manual blog creation',
      'Embed blog source',
      'Blog templates',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'green-check', 'green-check', 'green-check'],
      competitor: ['red-cross', 'green-check', 'red-cross', 'red-cross', 'red-cross'],
    },
  },
  {
    description: [
      'AI generated cover & content image',
      'Content Export Options',
      'AI generated graphs & charts',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'green-check'],
      competitor: ['red-cross', 'green-check', 'red-cross'],
    },
  },
];

const comparisonData: ComparisonItem[] = [
  {
    blogify: {
      description:
        'Allows you to generate full blog posts from videos, audio, PDFs, docs, URLs, and more. It’s a true content repurposing platform for bloggers.',
    },
    competitor: {
      description:
        'Primarily focused on text-based content; does not support multimedia-to-blog conversion.',
    },
  },
  {
    blogify: {
      description:
        'Blogify.ai allows content personalization using AI to match tone and niche based on source material.',
    },
    competitor: {
      description:
        'Frase.io offers personalization through SEO-driven briefs and content outlines tailored to search intent.',
    },
  },
  {
    blogify: {
      description:
        'Supports large files like lengthy videos or high-resolution documents for blog conversion.',
    },
    competitor: {
      description: 'Handles standard text inputs; lacks support for large multimedia files.',
    },
  },
  {
    blogify: {
      description:
        'Can process long video or audio files to extract and convert their content into structured blog articles.',
    },
    competitor: {
      description: "Not applicable, as it doesn't process audio or video content.",
    },
  },
  {
    blogify: {
      description:
        'Offers a one-click blog generation feature that automatically creates a full, SEO-ready blog from any content source.',
    },
    competitor: {
      description:
        'Assists in content creation but requires manual input and guidance; not fully automated.',
    },
  },
  {
    blogify: {
      description: 'Blogify.ai generates full blogs almost instantly from video/audio/text inputs.',
    },
    competitor: {
      description:
        'Frase.io delivers fast AI-generated content, particularly around brief creation and targeted paragraph generation.',
    },
  },
  {
    blogify: {
      description:
        'Provides assisted editing with section-wise blog suggestions, structure customization, and guided creation options.',
    },
    competitor: {
      description:
        'Offers content briefs and optimization suggestions but lacks collaborative writing tools.',
    },
  },
  {
    blogify: {
      description:
        'Built-in SEO tools provide scoring, optimization suggestions, and keyword guidance directly inside the editor.',
    },
    competitor: {
      description:
        'Provides detailed SEO analysis, keyword suggestions, and content optimization features.',
    },
  },
  {
    blogify: {
      description:
        'Can create complete blog posts ranging from short reads to long-form content automatically.',
    },
    competitor: {
      description:
        'Allows users to set target word counts and provides guidance to meet those goals.',
    },
  },
  {
    blogify: {
      description:
        'Supports over 150 global languages for blog creation, localization, and translation.',
    },
    competitor: {
      description: 'Primarily supports English; limited multilingual capabilities.',
    },
  },
  {
    blogify: {
      description:
        'Enables publishing directly to your own domain or subdomain via DNS configuration.',
    },
    competitor: {
      description:
        'Does not offer direct publishing; content must be manually exported and uploaded.',
    },
  },
  {
    blogify: {
      description:
        'Blogify.ai provides an SEO score and optimization recommendations for quality control.',
    },
    competitor: {
      description: 'Offers content scoring and optimization suggestions based on SERP analysis.',
    },
  },
  {
    blogify: {
      description:
        'Includes scheduling tools to plan and automate blog post publication at specific times.',
    },
    competitor: {
      description: 'Lacks built-in scheduling; relies on external platforms for post scheduling.',
    },
  },
  {
    blogify: {
      description:
        'Helps you monetize blogs with affiliate link suggestions, product embedding, and earning integrations.',
    },
    competitor: {
      description: 'Does not provide affiliate monetization features.',
    },
  },
  {
    blogify: {
      description:
        'Tracks post performance with built-in analytics tools like views, engagement, and SEO rankings.',
    },
    competitor: {
      description: 'Provides SEO analytics but lacks comprehensive content performance tracking.',
    },
  },
  {
    blogify: {
      description: 'Blogify.ai currently does not offer public API access.',
    },
    competitor: {
      description:
        'Frase.io provides API integration for programmatic content workflows and data access.',
    },
  },
  {
    blogify: {
      description:
        'Allows quick generation of intros, CTAs, and summary sections tailored for blog formats.',
    },
    competitor: {
      description: 'Does not offer writing snippet features.',
    },
  },
  {
    blogify: {
      description:
        'Extracts content directly from YouTube videos and turns them into well-structured blog posts.',
    },
    competitor: {
      description: 'Does not support YouTube integration for content creation.',
    },
  },
  {
    blogify: {
      description:
        'Enhances YouTube conversion with timestamp extraction, metadata analysis, and video SEO tools.',
    },
    competitor: {
      description: 'Does not have advanced YouTube integration features.',
    },
  },
  {
    blogify: {
      description:
        'Offers dedicated email and live chat support to assist with content creation, onboarding, and technical queries.',
    },
    competitor: {
      description: 'Provides support during business hours; no 24/7 support available.',
    },
  },
  {
    blogify: {
      description:
        'Converts various content types into blog posts. Transforms content from multiple sources including videos, audio files, documents, web pages, and ecommerce listings.',
    },
    competitor: {
      description: 'Limited to text-based content; does not support multimedia repurposing.',
    },
  },
  {
    blogify: {
      description: 'Blogify.ai supports blogs from diverse formats like video, audio, PDF, or DOC.',
    },
    competitor: {
      description: 'Frase.io supports blog posts, FAQs, briefs, and SEO-driven outlines.',
    },
  },
  {
    blogify: {
      description:
        'Gives users the option to auto-generate full blogs or manually edit and customize every section.',
    },
    competitor: {
      description: 'Primarily manual content creation with AI-assisted optimization.',
    },
  },
  {
    blogify: {
      description:
        'Enables embedding the original source of the blog (e.g., YouTube or webpage) directly in the post.',
    },
    competitor: {
      description: 'Does not support embedding of source content.',
    },
  },
  {
    blogify: {
      description:
        'Offers multiple pre-designed blog layouts for quick formatting and publishing consistency.',
    },
    competitor: {
      description: 'Offers content briefs but lacks predefined blog templates.',
    },
  },
  {
    blogify: {
      description:
        'Automatically generates featured images and content visuals based on blog topics.',
    },
    competitor: {
      description: 'Does not offer AI-generated images; users must source images separately.',
    },
  },
  {
    blogify: {
      description: 'Blogify.ai allows export of blogs and HTML for external publishing.',
    },
    competitor: {
      description:
        'Frase.io enables content download in PDF, DOCX, or direct copy-paste for CMS integration.',
    },
  },
  {
    blogify: {
      description:
        'Creates visuals like charts, graphs, and data representations directly within your blog post.',
    },
    competitor: {
      description: 'Does not provide tools for generating visual data representations.',
    },
  },
];

const BLOGIFY_VS_FRASE: ComparePageData = {
  metadata: {
    title: 'Blogify vs Frase.io: Detailed Feature Comparison',
    description:
      'Complete comparison of Blogify and Frase.io features, pricing, and capabilities for AI-powered content creation in 2025.',
    keywords: ['blogify vs frase.io', 'ai writing tools comparison', 'frase.io alternative'],
  },
  intro: {
    comparisonTitle: 'Comparison',
    mainTitle: 'Frase.io Alternative',
    description:
      'Blogify.ai outshines Frase.io by transforming videos, audio files, documents, and URLs into SEO-optimized, ready-to-publish blog posts. While Frase.io focuses on content optimization and research tools, Blogify is built for seamless content repurposing and automated blog creation — from start to finish. ✨',
    images: {
      competitorImage: FrasePost, // Replace with actual Frase.io image if available
      blogifyImage: Blogifypost,
    },
  },
  title: 'Unique Blogify Features vs Frase.io',
  description:
    'Blogify.ai stands apart from Frase.io with its ability to automate blog creation from multiple content formats, embed SEO, and publish through your own domain. Whether you’re a content creator, marketer, or agency — Blogify streamlines the entire blogging workflow with AI.',
  comparisonData,
  featureComparison,
  initialVisibleItems: 5,
  images: {
    blogifyLogo: BlogifyLogo,
    competitorLogo: FraseLogo, // Replace with actual Frase.io logo if available
  },
};

export default BLOGIFY_VS_FRASE;
