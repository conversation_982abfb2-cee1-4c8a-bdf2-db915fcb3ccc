import type { ComparePageData, ComparisonFeature, ComparisonItem } from '../../types';
import BlogifyLogo from '@img/Blogify_logo_white.svg';
import ContentBotPost from '@img/page-assets/comparison/contentbotpost.png';
import Blogifypost from '@img/page-assets/comparison/blogify-blog-post.png';
import ContentBotLogo from '@img/page-assets/comparison/contentbotlogo.png';

const featureComparison: ComparisonFeature[] = [
  {
    description: [
      'Create Blog From Anything',
      'Image Generation',
      'Source File Size',
      'Source File Duration',
      'Pricing Structure',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'green-check', 'green-check', 'green-check'],
      competitor: ['green-check', 'red-cross', 'red-cross', 'red-cross', 'green-check'],
    },
  },
  {
    description: [
      'Auto-Pilot Blog Creation',
      'Co-Pilot Blog Creation',
      'SEO Score & Optimization',
      'Word Count',
      'Language Support In Blogs',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'green-check', 'green-check', 'green-check'],
      competitor: ['green-check', 'green-check', 'green-check', 'green-check', 'green-check'],
    },
  },
  {
    description: [
      'Team Collaboration Features',
      'Publish on Your Own Site through Domain or Sub-Domain with DNS settings',
      'Schedule Blog Post',
      'Affiliate Monetization with AI',
      'Custom Use Cases',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'green-check', 'green-check', 'green-check'],
      competitor: ['green-check', 'red-cross', 'red-cross', 'red-cross', 'green-check'],
    },
  },
  {
    description: [
      'Analytics',
      'Writing Snippet (Addon)',
      'YouTube Connect (Addon)',
      'YouTube Connect Pro (Addon)',
      'Round the clock e-mail & chat support',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'green-check', 'green-check', 'green-check'],
      competitor: ['green-check', 'green-check', 'red-cross', 'red-cross', 'green-check'],
    },
  },
  {
    description: [
      'Content Rewriting & Editing',
      'Repurpose from other content (Video, Audio, Doc, PDF, Webpage, Ecommerce Page)',
      'Automate & manual blog creation',
      'Tone Customization',
      'Embed blog source',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'green-check', 'green-check', 'green-check'],
      competitor: ['green-check', 'red-cross', 'green-check', 'green-check', 'red-cross'],
    },
  },
  {
    description: [
      'Blog templates',
      'AI generated cover & content image',
      'User Interface and Experience',
      'AI generated graphs & charts',
    ],
    comparisons: {
      blogify: ['green-check', 'green-check', 'green-check', 'green-check'],
      competitor: ['green-check', 'green-check', 'green-check', 'red-cross'],
    },
  },
];


const comparisonData: ComparisonItem[] = [
  {
    blogify: {
      description:
        'Allows full blog generation from videos, audio files, PDFs, documents, URLs, and more — a true all-in-one content repurposing tool.',
    },
    competitor: {
      description:
        'Primarily designed for text-based input and idea generation; does not support multimedia or multi-format content conversions.',
    },
  },
  {
    blogify: {
      description:
        'Includes AI-generated cover and in-content images to visually enhance blogs without external design tools.',
    },
    competitor: {
      description: 'Focuses solely on text; lacks integrated AI image generation features.',
    },
  },
  {
    blogify: {
      description:
        'Supports large files, including lengthy videos or high-resolution documents, for seamless blog conversion.',
    },
    competitor: {
      description: 'Designed for short-form prompts or briefs; not optimized for large source files.',
    },
  },
  {
    blogify: {
      description:
        'Converts long videos or audio into structured, readable blog posts automatically.',
    },
    competitor: {
      description:
        'Cannot process long-form media; tailored more for short prompt-based content generation.',
    },
  },
  {
    blogify: {
      description:
        'Offers flexible pricing for solo creators and teams, including subscriptions and add-ons.',
    },
    competitor: {
      description:
        'Provides tiered pricing with both pay-as-you-go and subscription options to suit different usage levels.',
    },
  },
  {
    blogify: {
      description:
        'Features a one-click blog generator that creates full SEO-optimized blogs from any supported content type.',
    },
    competitor: {
      description:
        'Automation exists, but users often need to guide the structure and format manually for full-length blogs.',
    },
  },
  {
    blogify: {
      description:
        'Provides section-wise blog editing, structure customization, and guided content creation.',
    },
    competitor: {
      description:
        'Offers basic customization, but lacks deep collaborative editing or structured shaping during blog creation.',
    },
  },
  {
    blogify: {
      description:
        'Built-in SEO tools include keyword guidance, optimization tips, and post scoring within the editor.',
    },
    competitor: {
      description: 'Offers keyword suggestions but lacks integrated post-by-post SEO scoring tools.',
    },
  },
  {
    blogify: {
      description:
        'Automatically creates both short-form and long-form blog posts from various inputs.',
    },
    competitor: {
      description:
        'Generates content in shorter snippets; assembling full blogs usually requires manual work.',
    },
  },
  {
    blogify: {
      description: 'Supports over 150 languages for global blog creation, translation, and localization.',
    },
    competitor: {
      description: 'Also supports multiple languages, offering wide coverage for multilingual needs.',
    },
  },
  {
    blogify: {
      description:
        'Enables team collaboration, allowing multiple users to co-create, edit, and manage content projects.',
    },
    competitor: {
      description:
        'Includes team features and shared workspaces for collaborative content generation.',
    },
  },
  {
    blogify: {
      description:
        'Allows direct publishing to your domain or subdomain via DNS setup.',
    },
    competitor: {
      description:
        'Does not support direct publishing; users must copy content and publish manually.',
    },
  },
  {
    blogify: {
      description:
        'Features a built-in scheduling system to automate blog publishing at preferred times.',
    },
    competitor: {
      description:
        'No native scheduling; users must manually post to their own CMS or blog platform.',
    },
  },
  {
    blogify: {
      description:
        'Integrates blog monetization options like affiliate link suggestions and product embedding.',
    },
    competitor: {
      description:
        'Lacks monetization tools such as affiliate integration or ecommerce-friendly features.',
    },
  },
  {
    blogify: {
      description:
        'Highly customizable for content repurposing — from YouTube, podcasts, PDFs, ecommerce, and more.',
    },
    competitor: {
      description:
        'Offers flexible use cases like emails or product descriptions, but with a marketing-centered focus.',
    },
  },
  {
    blogify: {
      description:
        'Built-in analytics track views, SEO rankings, engagement, and post performance.',
    },
    competitor: {
      description: 'Limited analytics; external tools are needed for performance tracking.',
    },
  },
  {
    blogify: {
      description:
        'Supports fast generation of blog elements like intros, summaries, and CTAs tailored to format.',
    },
    competitor: {
      description:
        'Provides short-form generation tools but without dedicated blog-focused structuring.',
    },
  },
  {
    blogify: {
      description:
        'Converts YouTube videos directly into structured blog posts.',
    },
    competitor: {
      description:
        'No direct YouTube integration or video-to-blog transformation capability.',
    },
  },
  {
    blogify: {
      description:
        'Enhances YouTube repurposing with timestamp parsing, metadata use, and blog SEO tools.',
    },
    competitor: {
      description:
        'Lacks advanced features for extracting or repurposing YouTube video data.',
    },
  },
  {
    blogify: {
      description:
        'Offers live chat and email support for onboarding, troubleshooting, and content guidance.',
    },
    competitor: {
      description:
        'Provides ticket-based support, mainly during business hours.',
    },
  },
  {
    blogify: {
      description:
        'Combines auto-generation with manual editing, so users can refine output before publishing.',
    },
    competitor: {
      description:
        'Includes rewriting tools, but focused on adapting text rather than collaborative editing.',
    },
  },
  {
    blogify: {
      description:
        'Handles content from videos, audio, docs, web pages, and ecommerce platforms, converting them into SEO blogs.',
    },
    competitor: {
      description:
        'Designed for creating new content from scratch rather than transforming existing formats.',
    },
  },
  {
    blogify: {
      description:
        'Allows full automation or manual customization of blog content — section by section.',
    },
    competitor: {
      description:
        'Emphasizes quick AI-powered generation with less focus on manual crafting per section.',
    },
  },
  {
    blogify: {
      description:
        'Lets users adjust tone and voice (e.g., casual, formal) to match brand or audience needs.',
    },
    competitor: {
      description: 'Also offers tone control to align output with brand or campaign tone.',
    },
  },
  {
    blogify: {
      description:
        'Embeds the original content source (e.g., YouTube links or webpages) directly into blog posts.',
    },
    competitor: {
      description: 'Does not support embedding content sources inside generated blogs.',
    },
  },
  {
    blogify: {
      description:
        'Provides multiple ready-made blog layouts to ensure consistent formatting and style.',
    },
    competitor: {
      description:
        'Includes content templates geared more toward marketing than full blog structuring.',
    },
  },
  {
    blogify: {
      description:
        'Auto-generates featured and inline images based on blog topics for visual consistency.',
    },
    competitor: {
      description: 'Does not support image generation — visuals must be sourced separately.',
    },
  },
  {
    blogify: {
      description:
        'Features a user-friendly dashboard with intuitive design, perfect for non-technical users.',
    },
    competitor: {
      description:
        'Sleek, modern UI tailored to marketers with guided flows and pre-set templates.',
    },
  },
  {
    blogify: {
      description:
        'Creates blog visuals like charts, graphs, or data-based elements automatically.',
    },
    competitor: {
      description:
        'Does not support generation of visual data elements or embedded graphs.',
    },
  },
];

const BLOGIFY_VS_CONTENTBOT: ComparePageData = {
  metadata: {
    title: 'Blogify vs ContentBot: Detailed Feature Comparison',
    description:
      'Complete comparison of Blogify and ContentBot features, pricing, and capabilities for AI-powered content creation in 2025.',
    keywords: ['blogify vs contentbot', 'ai writing tools comparison', 'contentbot alternative'],
  },
  intro: {
    comparisonTitle: 'Comparison',
    mainTitle: 'ContentBot Alternative',
    description:
      'Blogify.ai outperforms ContentBot by transforming videos, podcasts, PDFs, and URLs into full SEO-optimized blog posts automatically. While ContentBot specializes in short-form and long-form AI writing, Blogify offers a broader repurposing toolkit, full blog automation, scheduling, monetization, and direct publishing — everything modern creators need to scale their content output. ✨',
    images: {
      competitorImage: ContentBotPost, // Use actual ContentBot image here
      blogifyImage: Blogifypost,
    },
  },
  title: 'Unique Blogify Features vs ContentBot',
  description:
    'Unlike ContentBot, Blogify.ai delivers robust automation, media-to-blog conversion, SEO optimization, and domain publishing. Ideal for creators and marketers who want to automate their entire blog workflow — not just generate content snippets.',
  comparisonData,
  featureComparison,
  initialVisibleItems: 5,
  images: {
    blogifyLogo: BlogifyLogo,
    competitorLogo: ContentBotLogo, // Use actual ContentBot logo here
  },
};

export default BLOGIFY_VS_CONTENTBOT;
