'use client';
import Image, { StaticImageData } from 'next/image';
import React from 'react';
import greenCheckIcon from '@img/icons/green-check.svg';
import redCrossIcon from '@img/icons/red-cross.svg';
import { ComparePageData } from '../../types';
import { GlowBox } from '@/app/(web)/partner-program/components';

interface ComparisonBetweenYesNoProps {
  title: string;
  description: string;
  featureComparison: ComparePageData['featureComparison'];
  images: {
    blogifyLogo: StaticImageData | string;
    competitorLogo: StaticImageData | string;
  };
}

export default function ComparisonBetweenYesNo({
  title,
  description,
  featureComparison = [],
  images,
}: ComparisonBetweenYesNoProps) {
  const getIcon = (icon: 'green-check' | 'red-cross' | string) => {
    if (icon === 'green-check') icon = greenCheckIcon;
    if (icon === 'red-cross') icon = redCrossIcon;

    return <Image src={icon} alt={`${icon} icon`} width={20} height={20} className="size-5" />;
  };

  if (!featureComparison || featureComparison.length === 0) {
    return (
      <div className="mt-32 text-center">
        <p className="text-red/50">No feature comparison data available</p>
      </div>
    );
  }

  return (
    <div className="mt-32">
      <h1 className="text-center text-4xl font-bold">{title}</h1>
      <p className="mt-6 text-center text-gray-500">{description}</p>

      <GlowBox className="mt-16">
        <table className="w-full">
          <thead className="rounded-t-xl text-left">
            <tr className="grid grid-cols-5">
              <th className="col-span-3">
                <div className="flex h-full flex-col justify-between p-5">
                  <p className="text-sm font-medium">Features</p>
                </div>
              </th>
              <th className="border-l border-secondary/25">
                <div className="flex h-full flex-col items-center justify-center p-5">
                  <Image src={images.blogifyLogo} alt="Blogify logo" width={100} height={25} />
                </div>
              </th>
              <th className="border-l border-secondary/25">
                <div className="flex h-full flex-col items-center justify-center p-5">
                  <Image
                    src={images.competitorLogo}
                    alt="Competitor logo"
                    width={100}
                    height={25}
                    className="rounded-lg"
                  />
                </div>
              </th>
            </tr>
          </thead>

          <tbody className="rounded-b-xl">
            {featureComparison.map((feature, rowIndex) => (
              <React.Fragment key={rowIndex}>
                <tr className="grid grid-cols-5 border-t border-secondary/25">
                  <td className="col-span-3 p-5">
                    {feature.description.map((desc, descIndex) => (
                      <p key={descIndex} className="py-2 text-sm">
                        {desc}
                      </p>
                    ))}
                  </td>
                  <td className="flex flex-col items-center border-l border-secondary/25 p-5">
                    {feature.comparisons.blogify.map((icon, iconIndex) => (
                      <div key={iconIndex} className="py-2">
                        {getIcon(icon)}
                      </div>
                    ))}
                  </td>
                  <td className="flex flex-col items-center border-l border-secondary/25 p-5">
                    {feature.comparisons.competitor.map((icon, iconIndex) => (
                      <div key={iconIndex} className="py-2">
                        {getIcon(icon)}
                      </div>
                    ))}
                  </td>
                </tr>
              </React.Fragment>
            ))}
          </tbody>
        </table>
      </GlowBox>
    </div>
  );
}
