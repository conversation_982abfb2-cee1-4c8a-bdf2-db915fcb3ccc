import type { VariantProps } from 'class-variance-authority';
import type { z } from 'zod';

import { forwardRef, useEffect, useState } from 'react';
import { XIcon } from 'lucide-react';
import { cva } from 'class-variance-authority';

import { Button } from './button';
import { Badge } from './badge';
import { cn } from '../lib/utils';

const parseTagOpt = (params: { tag: string; tagValidator: z.ZodString }) => {
  const { tag, tagValidator } = params;
  const parsedTag = tagValidator.safeParse(tag);

  if (parsedTag.success) {
    return parsedTag.data;
  }

  return null;
};

// Define the InputProps type
type InputProps = React.InputHTMLAttributes<HTMLInputElement>;

// caveat: :has() variant requires tailwind v3.4 or above: https://tailwindcss.com/blog/tailwindcss-v3-4#new-has-variant
const tagInputVariants = cva('flex min-h-10 w-full items-center flex-wrap gap-2 rounded-md', {
  variants: {
    variant: {
      default:
        'border border-neutral-200 bg-white px-3 py-2 text-sm ring-offset-white disabled:cursor-not-allowed disabled:opacity-50 has-[:focus-visible]:ring-2 has-[:focus-visible]:ring-neutral-950 has-[:focus-visible]:ring-offset-2 has-[:focus-visible]:outline-none dark:border-neutral-800 dark:bg-neutral-950 dark:ring-offset-neutral-950 dark:has-[:focus-visible]:ring-neutral-300',
      blank: 'min-h-0',
    },
  },
  defaultVariants: {
    variant: 'default',
  },
});

type TagInputProps = Omit<InputProps, 'value' | 'onChange'> & {
  value?: string[];
  onChange: (value: string[]) => void;
  tagValidator?: z.ZodString;
  inputClassName?: string;
} & VariantProps<typeof tagInputVariants>;

const TagInput = forwardRef<HTMLInputElement, TagInputProps>((props, ref) => {
  const {
    className,
    inputClassName,
    variant,
    value = [],
    onChange,
    tagValidator,
    ...domProps
  } = props;

  const [pendingDataPoint, setPendingDataPoint] = useState('');

  useEffect(() => {
    if (pendingDataPoint.includes(',')) {
      // Split by comma and filter/map in one pass
      const newTags = pendingDataPoint
        .split(',')
        .map((x) => x.trim())
        .filter((x) => x.length > 0)
        .map((trimmedX) => {
          if (tagValidator) {
            const validatedTag = parseTagOpt({ tag: trimmedX, tagValidator });
            return validatedTag;
          }
          return trimmedX;
        })
        .filter(Boolean) as string[]; // Type assertion to resolve the string | null issue

      // Create a Set to remove duplicates and combine with existing values
      const newDataPoints = new Set([...value, ...newTags]);
      onChange([...newDataPoints]);
      setPendingDataPoint('');
    }
  }, [pendingDataPoint, onChange, value, tagValidator]);

  const addPendingDataPoint = () => {
    if (pendingDataPoint) {
      if (tagValidator) {
        const validatedTag = parseTagOpt({ tag: pendingDataPoint, tagValidator });
        if (validatedTag) {
          const newDataPoints = new Set([...value, validatedTag]);
          onChange([...newDataPoints]);
          setPendingDataPoint('');
        }
      } else {
        const newDataPoints = new Set([...value, pendingDataPoint]);
        onChange([...newDataPoints]);
        setPendingDataPoint('');
      }
    }
  };

  return (
    <div className={cn(tagInputVariants({ variant }), className)}>
      {value.map((item) => (
        <Badge key={item} variant="tags">
          {item}
          <Button
            type="button"
            className="ml-1 size-3.5"
            variant="ghost"
            size="icon"
            onClick={() => onChange(value.filter((i) => i !== item))}
          >
            <XIcon />
          </Button>
        </Badge>
      ))}

      <input
        className={cn(
          'flex-1 outline-none placeholder:font-medium placeholder:!text-gray9 placeholder:text-13 dark:placeholder:text-neutral-400 h-8 px-0',
          inputClassName,
        )}
        value={pendingDataPoint}
        onChange={(e) => setPendingDataPoint(e.target.value)}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ',') {
            e.preventDefault();
            addPendingDataPoint();
          } else if (e.key === 'Backspace' && pendingDataPoint.length === 0 && value.length > 0) {
            e.preventDefault();
            onChange(value.slice(0, -1));
          }
        }}
        {...domProps}
        ref={ref}
      />
    </div>
  );
});

TagInput.displayName = 'TagInput';

export { TagInput };
