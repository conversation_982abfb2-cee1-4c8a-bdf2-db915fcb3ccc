export enum ImageProvider {
  OpenAI = 'openai',
  Google = 'google',
}

export enum ImageStyle {
  Anime = 'Anime',
  Cyberpunk = 'Cyberpunk',
  Fantasy = 'Fantasy',
  FineArt = 'Fine Art',
  Horror = 'Horror',
  Landscape = 'Landscape',
  Photorealistic = 'Photorealistic',
  Portraits = 'Portraits',
  SciFi = 'Sci-Fi',
  Surrealism = 'Surrealism',
}

export enum ImageModel {
  DallE2 = 'dall-e-2',
  DallE3 = 'dall-e-3',
  GPTImage1 = 'gpt-image-1',
  GeminiFlash2 = 'gemini-2.0-flash-preview-image-generation',
  VertexImagen3 = 'imagen-3.0-generate-002',
}

export enum ImageSize {
  '256x256' = '256x256',
  '512x512' = '512x512',
  '1024x1024' = '1024x1024',
  '1024x1792' = '1024x1792',
  '1792x1024' = '1792x1024',
  '1024x1536' = '1024x1536',
  '1536x1024' = '1536x1024',
  '1:1' = '1:1',
  '3:4' = '3:4',
  '4:3' = '4:3',
  '9:16' = '9:16',
  '16:9' = '16:9',
}

export enum ImageQuality {
  Standard = 'standard',
  Hd = 'hd',
}

export enum ImageType {
  Generated = 'generated',
  Uploaded = 'uploaded',
}

export enum ImageContent {
  // Open AI Dalle 2
  Dalle2SquareStandard = 'dall-e-2_1024x1024_standard',

  // Open AI Dalle 3
  Dalle3SquareStandard = 'dall-e-3_1024x1024_standard',
  Dalle3SquareHD = 'dall-e-3_1024x1024_hd',
  Dalle3PortraitStandard = 'dall-e-3_1024x1792_standard',
  Dalle3PortraitHD = 'dall-e-3_1024x1792_hd',
  Dalle3WideStandard = 'dall-e-3_1792x1024_standard',
  Dalle3WideHD = 'dall-e-3_1792x1024_hd',

  // Open AI GPT Image 1
  GPTImage1SquareStandard = 'gpt-image-1_1024x1024_standard',
  GPTImage1SquareHD = 'gpt-image-1_1024x1024_hd',
  GPTImage1PortraitStandard = 'gpt-image-1_1024x1792_standard',
  GPTImage1PortraitHD = 'gpt-image-1_1024x1792_hd',
  GPTImage1WideStandard = 'gpt-image-1_1792x1024_standard',
  GPTImage1WideHD = 'gpt-image-1_1792x1024_hd',

  // Google Gemini Flash
  GeminiFlashSquareStandard = 'gemini-2.0-flash-preview-image-generation_1:1_standard',

  // Google Vertex Imagen 3
  VertexImagen3SquareStandard = 'imagen-3.0-generate-002_1:1_standard',
  VertexImagen3PortraitClassicStandard = 'imagen-3.0-generate-002_3:4_standard',
  VertexImagen3ClassicStandard = 'imagen-3.0-generate-002_4:3_standard',
  VertexImagen3PortraitStandard = 'imagen-3.0-generate-002_9:16_standard',
  VertexImagen3WideStandard = 'imagen-3.0-generate-002_16:9_standard',
}
