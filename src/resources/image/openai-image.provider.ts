import type { ImageGenerationResponse } from './base.image.provider';
import type { GenerateImageDto } from './dto/generate-image.dto';

import { Injectable } from '@nestjs/common';

import { AiSdk } from '@/llm/sdk/AiSdk';

import { ImageProvider, ImageSize } from './image.enums';
import { BaseImageProvider } from './base.image.provider';

@Injectable()
export class OpenAIImageProvider extends BaseImageProvider {
  constructor(private readonly aiSdk: AiSdk) {
    super(ImageProvider.OpenAI);
  }

  async generateImage(input: GenerateImageDto): Promise<ImageGenerationResponse> {
    try {
      const size = this.validateSize(input.size as ImageSize);
      const model = input.model || 'gpt-image-1';
      let prompt = input.prompt;

      if (model.startsWith('dall-e')) {
        // DALL-E can't generate correct text
        prompt += ' Strictly do not use any text in the image.';
      }

      const result = await this.aiSdk.generateImage({
        provider: 'openai',
        prompt,
        model,
        size,
        quality: input.quality || 'standard',
        style: input.model === 'dall-e-3' ? 'vivid' : undefined,
        n: 1,
      });

      if (!result?.images?.length) {
        throw new Error('No images generated');
      }

      return { url: result.images[0] };
    } catch (error) {
      console.error('OpenAI Image Provider Error:', error);
      throw new Error(
        error.message || `Failed to generate image with Open AI ${input.model} model.`,
      );
    }
  }

  private validateSize(size?: ImageSize): ImageSize {
    if (Object.values(ImageSize).includes(size)) {
      return size;
    }
    return ImageSize['1024x1024'];
  }
}
