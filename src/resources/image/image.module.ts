import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

import { S3Service } from '@/common/services/s3.service';
import { AiSdk } from '@/llm/sdk/AiSdk';

import { CreditTransactionModule } from '../credit-transaction/credit-transaction.module';
import { GoogleImageProvider } from './google-image.provider';
import { OpenAIImageProvider } from './openai-image.provider';
import { ImageSchema, Image } from './image.model';
import { ImageController } from './image.controller';
import { ImageService } from './image.service';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Image.name, schema: ImageSchema }]),
    CreditTransactionModule,
  ],
  providers: [
    {
      provide: AiSdk,
      useFactory: () =>
        new AiSdk({
          provider: 'openai',
          model: 'gpt-4o',
          identifierName: 'image-generation',
          identifierValue: 'default',
        }),
    },
    GoogleImageProvider,
    OpenAIImageProvider,
    ImageService,
    S3Service,
  ],
  controllers: [ImageController],
  exports: [ImageService],
})
export class ImageModule {}
