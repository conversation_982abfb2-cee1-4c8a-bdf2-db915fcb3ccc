import type { ImageGenerationResponse } from './base.image.provider';
import type { GenerateImageDto } from './dto/generate-image.dto';

import { Injectable } from '@nestjs/common';

import { AiSdk } from '@/llm/sdk/AiSdk';

import { BaseImageProvider } from './base.image.provider';
import { ImageProvider } from './image.enums';

@Injectable()
export class GoogleImageProvider extends BaseImageProvider {
  constructor(private readonly aiSdk: AiSdk) {
    super(ImageProvider.Google);
  }

  async generateImage({ prompt, ...input }: GenerateImageDto): Promise<ImageGenerationResponse> {
    try {
      const size = this.validateSize(input.size);
      const model = input.model || 'gemini-2.0-flash-preview-image-generation';

      const result = await this.aiSdk.generateImage({
        provider: 'google',
        prompt,
        model,
        size,
        n: 1,
      });

      if (!result?.images?.length) {
        throw new Error('No images generated');
      }

      return { url: result.images[0] };
    } catch (error) {
      console.error('Google Image Provider Error:', error);
      throw new Error(
        error.message || `Failed to generate image with Google ${input.model} model.`,
      );
    }
  }

  private validateSize(size?: string): string {
    const supportedAspectRatios = ['1:1', '3:4', '4:3', '9:16', '16:9'];

    if (size && supportedAspectRatios.includes(size)) {
      return size;
    }

    return '1:1';
  }
}
