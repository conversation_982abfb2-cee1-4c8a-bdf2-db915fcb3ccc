import type { ContentType } from './credit-transaction.model';

export const CONTENT_COST: Record<ContentType, number> = {
  // Blog Generation:
  'blog-from-text': 5,
  'blog-from-media': 10,
  // 'blog-rephrase': 1

  // Writing Snippets Generation:
  // 'writing-snippet-small': 1,
  // 'writing-snippet-medium': 2,
  // 'writing-snippet-big': 3,

  // YouTube Content Generation:
  thumbnail: 2,
  // 'thumbnail-xl': 5,
  short: 10,
  // 'short-mini': 5,
  // 'short-download': 5,
  chapters: 10,
  transcript: 5,
  summary: 2,
  description: 2,
  'social-post': 2,
  social_post_facebook: 2,
  social_post_twitter: 2,
  social_post_linkedin: 2,
  title: 1,
  tag: 1,
  hashtag: 1,

  // Images
  // OpenAI Dalle 2
  'dall-e-2_1024x1024_standard': 1,

  // OpenAI Dalle 2
  'dall-e-3_1024x1024_standard': 3,
  'dall-e-3_1024x1024_hd': 5,
  'dall-e-3_1024x1792_standard': 5,
  'dall-e-3_1024x1792_hd': 8,
  'dall-e-3_1792x1024_standard': 5,
  'dall-e-3_1792x1024_hd': 8,

  // OpenAI GPT Image 1
  'gpt-image-1_1024x1024_standard': 2,
  'gpt-image-1_1024x1024_hd': 3,
  'gpt-image-1_1024x1792_standard': 2,
  'gpt-image-1_1024x1792_hd': 3,
  'gpt-image-1_1792x1024_standard': 2,
  'gpt-image-1_1792x1024_hd': 3,

  // Google Gemini Flash
  'gemini-2.0-flash-preview-image-generation_1:1_standard': 2,

  // Google Vertex Imagen 3
  'imagen-3.0-generate-002_1:1_standard': 3,
  'imagen-3.0-generate-002_3:4_standard': 3,
  'imagen-3.0-generate-002_4:3_standard': 3,
  'imagen-3.0-generate-002_9:16_standard': 3,
  'imagen-3.0-generate-002_16:9_standard': 3,
};
