import type { FilterQuery as RootFilterQuery, Model } from 'mongoose';
import type { ObjectId } from 'mongodb';

import { mixin } from '@nestjs/common';

export function BaseService<Resource>() {
  class BaseServiceMixin {
    protected model: Model<Resource>;

    constructor(model: Model<Resource>) {
      this.model = model;
    }

    async findMany(
      filter: RootFilterQuery<Resource>,
      options?: Partial<Omit<MongoParams<Resource>, 'filter'>>,
    ): Promise<Resource[]> {
      const query = this.model.find(filter);

      Object.keys(options || {}).forEach((key: keyof MongoParams<Resource>) => {
        if (options[key]) {
          query[key](options[key]);
        }
      });

      return query.exec();
    }

    async findOne(
      filter: RootFilterQuery<Resource>,
      options?: Partial<Pick<MongoParams<Resource>, 'sort' | 'populate'>>,
    ): Promise<Resource | null> {
      const query = this.model.findOne(filter);

      Object.keys(options || {}).forEach((key: keyof MongoParams<Resource>) => {
        if (options[key]) {
          query[key](options[key]);
        }
      });

      return query.exec();
    }

    async findById(
      id: string | ObjectId,
      options?: Partial<Pick<MongoParams<Resource>, 'filter' | 'populate'>>,
    ): Promise<Resource> {
      const query = this.model.findById(id, null, options?.filter ? options.filter : {});

      if (options?.populate?.length) {
        query.populate(options.populate);
      }

      return query.exec() as Resource;
    }

    async count(filter: RootFilterQuery<Resource>): Promise<number> {
      return this.model.countDocuments(filter).exec();
    }

    async create(data: Partial<Resource>): Promise<Resource> {
      const createdItem = new this.model(data);
      return createdItem.save() as Resource;
    }

    async update(id: string | ObjectId, data: Partial<Resource>): Promise<Resource> {
      return this.model.findByIdAndUpdate(id, data).exec() as Resource;
    }

    async updateOne(filter: RootFilterQuery<Resource>, data: Partial<Resource>): Promise<Resource> {
      return this.model.findOneAndUpdate(filter, data).exec() as Resource;
    }

    async delete(id: string | ObjectId): Promise<{ success: boolean }> {
      await this.update(id, { deleted: true } as Resource);
      return { success: true };
    }

    async deleteOne(filter: RootFilterQuery<Resource>): Promise<{ success: boolean }> {
      await this.updateOne(filter, { deleted: true } as Resource);
      return { success: true };
    }

    async destroy(id: string | ObjectId): Promise<{ success: boolean }> {
      await this.model.findByIdAndDelete(id, { deleted: true } as Resource);
      return { success: true };
    }
  }

  const service = mixin(BaseServiceMixin);
  return service;
}
