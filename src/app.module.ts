import { Lo<PERSON>, Module, OnApplicationShutdown, MiddlewareConsumer } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ServeStaticModule } from '@nestjs/serve-static';
import { ThrottlerModule } from '@nestjs/throttler';
import { MongooseModule } from '@nestjs/mongoose';
import { MulterModule } from '@nestjs/platform-express';
import { diskStorage } from 'multer';
import { BullModule } from '@nestjs/bull';
import { APP_FILTER } from '@nestjs/core';
import { JwtModule } from '@nestjs/jwt';
import { join } from 'path';

import { GlobalExceptionFilter } from '@/common/filters/global-exception.filter';
import { GatewayModule } from '@/modules/gateway/gateway.module';
import { SlackService } from '@/common/services/slack.service';
import { CacheModule } from '@/modules/cache/cache.module';
import { MailModule } from '@/modules/mail/mail.module';
import { AuthModule } from '@/auth/auth.module';
import { S3Service } from '@/common/services/s3.service';
import config from '@/common/configs/config';

// Resources
import { CreditTransactionModule } from '@/resources/credit-transaction/credit-transaction.module';
import { WebsiteSubscriberModule } from '@/resources/website-subscriber/website-subscriber.module';
import { BlogCategoryModule } from '@/resources/blog-category/blog-category.module';
import { NotificationModule } from '@/resources/notification/notification.module';
import { SnippetTypeModule } from '@/resources/snippet-type/snippet-type.module';
import { TransactionModule } from '@/resources/transaction/transaction.module';
import { BlogSourceModule } from '@/resources/blog-source/blog-source.module';
import { CategoryModule } from '@/resources/category/category.module';
import { SettingsModule } from '@/resources/settings/settings.module';
import { ProductModule } from '@/resources/product/product.module';
import { WebsiteModule } from '@/resources/website/website.module';
import { WalletModule } from '@/resources/wallet/wallet.module';
import { ImageModule } from '@/resources/image/image.module';
import { BlogModule } from '@/blog/blog.module';
import { UserModule } from '@/user/user.module';

// Integrations
import { PublishingModule } from './publishing/publishing.module';
import { GoogleAdsModule } from '@/integrations/google-ads/google-ads.module';
import { YoutubeModule } from '@/youtube/youtube.module';
import { GoogleModule } from '@/integrations/socials/google/google.module';
import { SlackModule } from '@/integrations/internal/slack/slack.module';

import { AffiliateLinkTrackingModule } from '@/monetization/affiliate-link-tracking/affiliate-link-tracking.module';
import { CorrelationIdMiddleware } from '@/common/middleware/correlation-id.middleware';
import { WritingSnippetsModule } from '@/writing-snippets/writing-snippets.module';
import { BlogGenerationModule } from '@/blog-generation/blog-generation.module';
import { TranscriptionModule } from '@/transcription/transcription.module';
import { AppShutdownService } from '@/app.shutdown.service';
import { BlogifyMediaModule } from '@/blogify-media/blogify-media.module';
import { MonetizationModule } from '@/monetization/monetization.module';
import { SubscriptionModule } from '@/subscription/subscription.module';
import { TranscribeModule } from '@/transcribe/transcribe.module';
import { CustomPinoLogger } from '@/common/logger/logger';
import { AffiliateModule } from '@/affiliate/affiliate.module';
import { PublicApiModule } from '@/public-api/public-api.module';
import { AnalyticsModule } from '@/analytics/analytics.module';
import { ShutdownModule } from '@/common/shutdown/shutdown.module';
import { AssemblyModule } from '@/assembly/assembly.module';
import { PaymentsModule } from '@/payments/payments.module';
import { TrackingModule } from '@/tracking/tracking.module';
import { ContextModule } from '@/context/context.module';
import { SpinnerModule } from '@/spinner/spinner.module';
import { HealthModule } from '@/health/health.module';
import { EventModule } from '@/event/event.module';
import { IdeaModule } from '@/idea/idea.module';
import { JobModule } from '@/job/job.module';
import { SeoModule } from '@/seo/seo.module';
import { MeModule } from '@/me/me.module';

import { AppController } from './app.controller';
import { ProjectModule } from './project/project.module';
import { IdeaResearchModule } from './idea-research/idea-research.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [config],
      cache: true,
    }),
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (config: ConfigService) => ({
        uri: config.get<string>('MONGO_URL'), // Loaded from .ENV
        ...(config.get('NODE_ENV') === 'development'
          ? { useUnifiedTopology: true, useNewUrlParser: true }
          : {}),
      }),
    }),
    BullModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (config: ConfigService) => ({
        url: config.get<string>('REDIS_URL'),
        limiter: {
          max: 10,
          duration: 10000,
        },
      }),
    }),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (config: ConfigService) => ({
        secret: config.get<string>('JWT_SECRET'),
        signOptions: { expiresIn: '24h' },
      }),
    }),
    MulterModule.register({
      limits: {
        fileSize: 20 * 1024 * 1024, // maximum file size limit 10MB
      },
      storage: diskStorage({
        destination: './uploads',
        filename: (req, file, cb) => {
          const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1e9);
          cb(null, file.fieldname + '-' + uniqueSuffix + '-' + file.originalname);
        },
      }),
    }),
    ThrottlerModule.forRoot({
      throttlers: [
        {
          ttl: 60000,
          limit: 10,
        },
      ],
    }),
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '..', 'public'),
    }),
    CacheModule.forRoot(),
    ShutdownModule,
    WritingSnippetsModule,
    BlogGenerationModule,
    TranscriptionModule,
    SubscriptionModule,
    BlogifyMediaModule,
    TranscribeModule,
    AnalyticsModule,
    PublicApiModule,
    AssemblyModule,
    PaymentsModule,
    TrackingModule,
    ContextModule,
    GatewayModule,
    EventModule,
    ImageModule,
    AuthModule,
    MailModule,
    JobModule,
    MeModule,
    // Resources
    CreditTransactionModule,
    WebsiteSubscriberModule,
    BlogCategoryModule,
    NotificationModule,
    SnippetTypeModule,
    TransactionModule,
    BlogSourceModule,
    CategoryModule,
    SettingsModule,
    WebsiteModule,
    ProductModule,
    WalletModule,
    BlogModule,
    ProjectModule,
    IdeaResearchModule,
    UserModule,
    // Integrations
    PublishingModule,
    GoogleAdsModule,
    YoutubeModule,
    GoogleModule,
    SlackModule,
    // Monetization
    AffiliateLinkTrackingModule,
    MonetizationModule,
    AffiliateModule,
    // SEO
    SeoModule,
    SpinnerModule,
    HealthModule,
    IdeaModule,
  ],
  controllers: [AppController],
  providers: [
    {
      provide: Logger,
      useClass: CustomPinoLogger,
    },
    {
      provide: 'LOGGER_CONTEXT',
      useValue: 'AppModule',
    },
    { provide: APP_FILTER, useClass: GlobalExceptionFilter },
    AppShutdownService,
    SlackService,
    S3Service,
  ],
})
export class AppModule implements OnApplicationShutdown {
  private logger = new Logger(AppModule.name);

  constructor(private readonly appShutdownService: AppShutdownService) {}

  // Configure middleware to add correlation ID to each request
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(CorrelationIdMiddleware).forRoutes('*');
  }

  async onApplicationShutdown(signal: string) {
    await this.appShutdownService.onApplicationShutdown(signal);
  }
}
