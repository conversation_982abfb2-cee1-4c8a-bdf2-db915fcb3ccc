import { AffiliateLinkTracking } from '@/monetization/affiliate-link-tracking/affiliate-link-tracking.model';
import { AffiliateAction } from '@/monetization/interfaces/affiliate-action.interface';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { IncomingWebhook } from '@slack/webhook';

interface SlackNotificationDto {
  bid?: string;
  email?: string;
  name?: string;
  businessName?: string;
  foundUsFrom?: string;
  isShopifyUser?: boolean;
  plan?: string;
  amount?: number;
  coupon?: string;
  customMessage?: string;
}

@Injectable()
export class SlackService {
  private readonly logger = new Logger(SlackService.name);
  private readonly slackSignup: IncomingWebhook;
  private readonly slackPaidSubscription: IncomingWebhook;
  private readonly slackFreeTrial: IncomingWebhook;
  private readonly cancelledSubscription: IncomingWebhook;
  private readonly affiliateMonitoring: IncomingWebhook;
  private readonly blacklistedEmailDomains = ['gmx.com'];
  constructor(private readonly configService: ConfigService) {
    this.slackSignup = new IncomingWebhook(
      this.configService.get('SLACK_WEBHOOK_CHANNEL_MONITOR_SIGNUP'),
    );
    this.slackPaidSubscription = new IncomingWebhook(
      this.configService.get('SLACK_WEBHOOK_CHANNEL_PAID_PLAN_SUBSCRIPTION'),
    );
    this.slackFreeTrial = new IncomingWebhook(
      this.configService.get('SLACK_WEBHOOK_CHANNEL_FREE_TRIAL_SUBSCRIPTION'),
    );
    this.cancelledSubscription = new IncomingWebhook(
      this.configService.get('SLACK_WEBHOOK_CHANNEL_CANCELLED_SUBSCRIPTION'),
    );
    this.affiliateMonitoring = new IncomingWebhook(
      this.configService.get('SLACK_WEBHOOK_CHANNEL_MONITOR_AFFILIATE_SALES'),
    );
  }

  isBlacklistedEmail = (email: string) =>
    this.blacklistedEmailDomains.includes(email?.split('@')[1]);

  async sendSignupNotification({
    email,
    name,
    businessName,
    foundUsFrom,
    isShopifyUser,
  }: SlackNotificationDto) {
    if (process.env.NODE_ENV !== 'production' || this.isBlacklistedEmail(email)) {
      return;
    }
    try {
      const attachments = [];
      if (foundUsFrom) {
        attachments.push({
          color: '#f0f8ff',
          text: `found us from ${foundUsFrom}`,
        });
      }
      if (isShopifyUser) {
        attachments.push({
          color: '#96bf48',
          text: `Shopify user`,
        });
      }
      await this.slackSignup.send({
        text: `*${email}* *${name}* | *${businessName}* has signed up :tada:`,
        attachments,
      });
    } catch (error) {
      this.logger.error('Slack webhook failed', error?.message);
    }
  }

  async sendPaidPlanNotification({
    email,
    name,
    businessName,
    plan,
    coupon,
    isShopifyUser,
    customMessage,
  }: SlackNotificationDto) {
    if (process.env.NODE_ENV !== 'production' || this.isBlacklistedEmail(email)) {
      return;
    }
    try {
      const attachments = [];
      if (customMessage) {
        attachments.push({
          color: '#4b0082',
          text: customMessage,
        });
      }
      if (isShopifyUser) {
        attachments.push({
          color: '#96bf48',
          text: `Shopify user`,
        });
      }
      if (coupon) {
        attachments.push({
          color: '#00ff00',
          text: `coupon ${coupon}`,
        });
      }
      await this.slackPaidSubscription.send({
        text: `*${name}*-*${businessName}* *${email}* has subscribed to *${plan}* plan :tada: :partying_face:`,
        attachments,
      });
    } catch (error) {
      this.logger.error('Slack webhook failed', error?.message);
    }
  }

  async sendFreeTrialNotification({
    email,
    name,
    businessName,
    plan,
    coupon,
  }: SlackNotificationDto) {
    if (process.env.NODE_ENV !== 'production' || this.isBlacklistedEmail(email)) {
      return;
    }
    try {
      const attachments = [];
      if (coupon) {
        attachments.push({
          color: '#00ff00',
          text: `coupon ${coupon}`,
        });
      }

      await this.slackFreeTrial.send({
        text: `*${name}*-*${businessName}* *${email}* has started trial for *${plan}* plan :+1:`,
        attachments,
      });
    } catch (error) {
      this.logger.error('Slack webhook failed', error?.message);
    }
  }

  async sendCancelSubscriptionNotification({
    email,
    name,
    businessName,
    plan,
    customMessage,
  }: SlackNotificationDto) {
    if (process.env.NODE_ENV !== 'production' || this.isBlacklistedEmail(email)) {
      return;
    }
    if (!email || !name || !businessName || !plan) {
      return;
    }
    try {
      const attachments = [];
      if (customMessage) {
        attachments.push({
          color: '#4b0082',
          text: customMessage,
        });
      }
      await this.cancelledSubscription.send({
        text: `*${name}*-*${businessName}* *${email}* has cancelled the *${plan}* plan :sob:`,
        attachments,
      });
    } catch (error) {
      this.logger.error('Slack webhook failed', error?.message);
    }
  }

  async sendCancelSubscriptionOnTrialEndNotification({
    name,
    businessName,
    plan,
    customMessage,
  }: SlackNotificationDto) {
    if (process.env.NODE_ENV !== 'production') {
      return;
    }
    if (!name || !businessName || !plan) {
      return;
    }
    try {
      const attachments = [];
      if (customMessage) {
        attachments.push({
          color: '#4b0082',
          text: customMessage,
        });
      }
      await this.cancelledSubscription.send({
        text: `*${name}*-*${businessName}* *${plan}* plan canceled after trial end due to payment failure. :sob:`,
        attachments,
      });
    } catch (error) {
      this.logger.error('Slack webhook failed', error?.message);
    }
  }

  async sendAffiliateActionsFound(actions: number, startDate: Date, endDate: Date) {
    try {
      await this.affiliateMonitoring.send({
        text: `:money_with_wings: Found ${actions} new affiliate actions`,
        blocks: [
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: `:money_with_wings: Found ${actions} new affiliate actions\n*Time Range:* ${startDate.toISOString()} to ${endDate.toISOString()}`,
            },
          },
        ],
      });
    } catch (error) {
      this.logger.error('Slack webhook failed', error?.message);
    }
  }

  async sendAffiliatePotentialMatches(action: AffiliateAction, matches: AffiliateLinkTracking[]) {
    try {
      await this.affiliateMonitoring.send({
        text: `:mag: Found ${matches.length} potential matches for action`,
        blocks: [
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: [
                `:mag: Found ${matches.length} potential matches for action:`,
                `*Campaign:* ${action.campaignName} (${action.campaignId})`,
                `*Amount:* :moneybag: ${action.amount} ${action.currency}`,
                `*Commission:* :money_mouth_face: ${action.commission} ${action.currency}`,
                `*Purchase Date:* ${action.purchaseDate}`,
                `*Status:* ${action.state}`,
                '',
                '*Potential Matches:*',
                matches
                  .map(
                    (match) =>
                      `• Business: ${match.bid} | Blog: ${
                        match.blogId
                      }\n  Link: ${match.affiliateLink.substring(0, 50)}...`,
                  )
                  .join('\n'),
              ].join('\n'),
            },
          },
        ],
      });
    } catch (error) {
      this.logger.error('Slack webhook failed', error?.message);
    }
  }

  async sendAffiliateBestMatch(action: AffiliateAction, match: AffiliateLinkTracking) {
    try {
      await this.affiliateMonitoring.send({
        text: `:star-struck: Found best match for affiliate action`,
        blocks: [
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: [
                `:star-struck: Found best match for affiliate action:`,
                `*Campaign:* ${action.campaignName} (${action.campaignId})`,
                `*Amount:* :moneybag: ${action.amount} ${action.currency}`,
                `*Commission:* :money_mouth_face: ${action.commission} ${action.currency}`,
                `*Purchase Date:* ${action.purchaseDate}`,
                `*Status:* ${action.state}`,
                '',
                '*Best Match:*',
                `• Business ID: ${match.bid}`,
                `• Blog ID: ${match.blogId}`,
                `• Link: ${match.affiliateLink.substring(0, 50)}...`,
                `• Product: ${match.productName}`,
                `• Price: :dollar: ${match.productPrice}`,
              ].join('\n'),
            },
          },
        ],
      });
    } catch (error) {
      this.logger.error('Slack webhook failed', error?.message);
    }
  }

  async sendAffiliateNoMatch(action: AffiliateAction) {
    try {
      await this.affiliateMonitoring.send({
        text: `:thinking_face: No best match found for affiliate action`,
        blocks: [
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: [
                `:thinking_face: No best match found for affiliate action:`,
                `*Campaign:* ${action.campaignName} (${action.campaignId})`,
                `*Amount:* :moneybag: ${action.amount} ${action.currency}`,
                `*Commission:* :money_mouth_face: ${action.commission} ${action.currency}`,
                `*Purchase Date:* ${action.purchaseDate}`,
                `*Status:* ${action.state}`,
                '',
                '*Note:* :detective: Had potential matches but none met the confidence threshold.',
              ].join('\n'),
            },
          },
        ],
      });
    } catch (error) {
      this.logger.error('Slack webhook failed', error?.message);
    }
  }

  async sendAffiliateError(error: Error) {
    try {
      await this.affiliateMonitoring.send({
        text: `:warning: Error in affiliate sale monitoring`,
        blocks: [
          {
            type: 'section',
            text: {
              type: 'mrkdwn',
              text: [
                `:warning: Error in affiliate sale monitoring:`,
                `:red_circle: Error Message:`,
                `\`\`\`${error.message}\`\`\``,
              ].join('\n'),
            },
          },
        ],
      });
    } catch (error) {
      this.logger.error('Slack webhook failed', error?.message);
    }
  }
}
