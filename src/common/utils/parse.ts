// بسم الله الرحمن الرحيم
/**
 * The purpose of this module is to house various HTML parsing functions.
 */

import { load } from 'cheerio';
import { range } from 'lodash';
import { isValidImageUrl, resolveUrl } from './url';

/**
 * The parsed tree's Root
 */
type Root = cheerio.Root;

/**
 * Removes unnecessary whitespace from text
 * @param text - The text to be cleaned
 * @returns The cleaned text
 */
export const pruneWhiteSpace = (text: string) => text.replace(/\s{2,}/g, ' ');

export const containsTag = ($: cheerio.Root, tag: string) => $(tag).length > 0;

export const makeRoot = (html: string) => load(html);
/**
 * Container for various functions to extract text from parsed HTML tree
 */
export const extract = {
  pageTitle: ($: Root) => $('title').text(),

  metaDescription: ($: Root) => $('meta[name="description"]').attr('content') || '',

  featuredImage: ($: Root, baseUrl: string): string | undefined => {
    const candidates: string[] = [];

    // 1. Open Graph image (most reliable for social sharing)
    const ogImage = $('meta[property="og:image"]').attr('content');
    if (ogImage) candidates.push(ogImage);

    // 2. Twitter Card image
    const twitterImage =
      $('meta[name="twitter:image"]').attr('content') ||
      $('meta[property="twitter:image"]').attr('content');
    if (twitterImage) candidates.push(twitterImage);

    // 3. Article image (common in news sites and blogs)
    const articleImage = $('meta[property="article:image"]').attr('content');
    if (articleImage) candidates.push(articleImage);

    // 4. Schema.org image
    const schemaImage = $('meta[itemprop="image"]').attr('content');
    if (schemaImage) candidates.push(schemaImage);

    // 5. Link rel image_src (less common but still used)
    const linkImage = $('link[rel="image_src"]').attr('href');
    if (linkImage) candidates.push(linkImage);

    // 6. First image in main content areas (fallback)
    const contentImages = $('main img, article img, .content img, #content img')
      .first()
      .attr('src');
    if (contentImages) candidates.push(contentImages);

    // 7. First image with specific classes that might indicate featured images
    const featuredImages = $('img.featured-image, img.hero-image, img.banner-image, img.post-image')
      .first()
      .attr('src');
    if (featuredImages) candidates.push(featuredImages);

    // 8. Largest image as last resort (skip very small images like icons)
    if (candidates.length === 0) {
      $('img').each((_, elem) => {
        const $img = $(elem);
        const src = $img.attr('src');
        const width = parseInt($img.attr('width') || '0');
        const height = parseInt($img.attr('height') || '0');

        // Skip very small images (likely icons/logos)
        if (src && (width === 0 || width > 200) && (height === 0 || height > 200)) {
          candidates.push(src);
        }
      });
    }

    // Process candidates: resolve URLs and validate
    for (const candidate of candidates) {
      if (!candidate) continue;

      const resolvedUrl = resolveUrl(baseUrl, candidate.trim());

      if (isValidImageUrl(resolvedUrl)) {
        return resolvedUrl;
      }
    }

    return undefined;
  },

  bodyText: function parse($: Root) {
    return $(
      'html body *' +
        range(1, 7)
          .map((x) => `:not(h${x})`)
          .join(''),
    )
      .contents()
      .filter(function () {
        return this.type === 'text';
      })
      .filter(function () {
        return !['script', 'style', 'noscript'].includes(this.parent.name);
      })
      .map(function () {
        return $(this).text().trim();
      })
      .get()
      .filter((text) => text.length)
      .join(' ');
  },
  tagText: function ($: cheerio.Root, tag: string): string[] {
    return $(tag)
      .contents()
      .map(function () {
        return $(this).text().trim();
      })
      .get()
      .filter((text) => text.length);
  },
  tagsText: (
    $: Root,
    tags: string[] = [
      'article',
      'main',
      'section',
      'h1',
      'h2',
      'h3',
      'h4',
      'h5',
      'h6',
      'p',
      'table',
      'ol',
      'ul',
    ],
  ) => {
    let importantText = '';
    tags.forEach((tag) => {
      $(tag).each((_, elem) => {
        importantText += $(elem).text().trim() + ' ';
      });
    });
    return importantText;
  },
} as const;
