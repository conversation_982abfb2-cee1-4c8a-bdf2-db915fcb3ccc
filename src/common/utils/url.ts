import { BlogSourceName } from '@blog/blog.enums';
import axios from 'axios';

/**
 * Replaces the last fragment of a URL with the provided slug.
 * @param url - The URL string in the format `https://${string}/${string}`
 * @param slug - The slug to replace the last fragment with
 * @returns The new URL string with the slug as the last fragment
 */
const replaceUrlSlug = (url: `https://${string}/${string}`, slug: string): string =>
  url.split('/').slice(0, -1).concat(slug).join('/');

const isYouTubeUrl = (url: string) => {
  const youtubeRegex =
    /^(?:https?:\/\/)?(?:www\.)?(?:m\.)?(?:music\.)?(?:youtube\.com\/(?:[^/]+\?.*v=|embed\/|v\/|shorts\/|live\/)|youtu\.be\/)([a-zA-Z0-9_-]{11})/;
  return youtubeRegex.test(url);
};

function getDomainFromUrl(input) {
  try {
    const url = new URL(input.includes('://') ? input : 'http://' + input);
    const hostname = url.hostname;

    const domainRegex = /^(?:[a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}$/;
    return domainRegex.test(hostname) ? hostname : false;
  } catch (_) {
    return false;
  }
}

const isValidUrl = (url: string) => {
  try {
    new URL(url); // throw upon ill-formed url
    return true;
  } catch {
    return false;
  }
};
const EmbeddingSupportedBlogSources = [
  BlogSourceName.TikTok,
  BlogSourceName.YouTube,
  BlogSourceName.Rumble,
  BlogSourceName.Vimeo,
  BlogSourceName.SoundCloud,
  BlogSourceName.PodBean,
] as const;

const oembedBases: Record<(typeof EmbeddingSupportedBlogSources)[number], string> = {
  TikTok: 'https://www.tiktok.com/oembed',
  YouTube: 'https://www.youtube.com/oembed?format=json',
  Vimeo: 'https://vimeo.com/api/oembed.json',
  Rumble: 'https://rumble.com/api/Media/oembed.json',
  SoundCloud: 'https://soundcloud.com/oembed?format=json',
  PodBean: 'https://www.podbean.com/media/oembed?format=json',
};

async function getEmbedCode(url: string, platform: keyof typeof oembedBases) {
  const oembedUrl = new URL(oembedBases[platform]);
  oembedUrl.searchParams.append('url', url);

  const response = await axios.get<{ html: string }>(oembedUrl.toString());
  return response.data.html;
}

function normalizeUrl(url: string): string {
  try {
    const parsedUrl = new URL(url.startsWith('http') ? url : `https://${url}`);

    // Normalize: lowercase hostname, remove trailing slash from pathname, remove www if needed
    let normalizedHostname = parsedUrl.hostname.toLowerCase();

    // Optional: remove 'www.' prefix
    if (normalizedHostname.startsWith('www.')) {
      normalizedHostname = normalizedHostname.replace('www.', '');
    }

    const normalizedPath = parsedUrl.pathname.replace(/\/+$/, ''); // Remove trailing slash
    const normalized = `${parsedUrl.protocol}//${normalizedHostname}${normalizedPath}`;

    return normalized;
  } catch (_) {
    throw new Error(`Invalid URL provided: ${url}`);
  }
}

function resolveUrl(baseUrl: string, relativeUrl: string): string {
  try {
    return new URL(relativeUrl, baseUrl).href;
  } catch {
    return relativeUrl;
  }
}

function isValidImageUrl(url: string): boolean {
  if (!url) return false;

  // Check for common image extensions
  const imageExtensions = /\.(jpg|jpeg|png|gif|webp|svg|bmp|ico)(\?.*)?$/i;
  if (imageExtensions.test(url)) return true;

  // Check for data URLs
  if (url.startsWith('data:image/')) return true;

  // Some images might not have extensions but are still valid
  // We'll allow them through basic validation
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

export {
  EmbeddingSupportedBlogSources,
  getDomainFromUrl,
  getEmbedCode,
  isValidUrl,
  isYouTubeUrl,
  normalizeUrl,
  replaceUrlSlug,
  isValidImageUrl,
  resolveUrl,
};
