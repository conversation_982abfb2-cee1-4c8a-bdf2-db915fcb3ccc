import type { Config } from './config.interface';

const getEnv = () => (process.env.ENV_STAGE || process.env.NODE_ENV) as Config['environment'];

const getRedisConfig = () => {
  const redisUrl = process.env.REDIS_URL;

  if (!redisUrl) {
    throw new Error('REDIS_URL is not defined');
  }

  const parsedUrl = new URL(redisUrl);

  return {
    host: parsedUrl.hostname,
    port: parseInt(parsedUrl.port, 10),
    username: parsedUrl.username || undefined,
    password: parsedUrl.password || undefined,
  };
};

const config: Config = {
  nest: {
    port: parseInt(process.env.PORT, 10) || 7777,
  },
  cors: {
    enabled: true,
  },
  security: {
    secret: process.env.JWT_SECRET || '',
    expiresIn: '14d',
    refreshIn: '',
    bcryptSaltOrRound: 10,
  },

  environment: getEnv() || 'development',
  isProd: getEnv() === 'production',
  isStaging: getEnv() === 'staging',
  isDev: getEnv() === 'development',
  isTest: getEnv() === 'test',

  stripeProductId: process.env.STRIPE_PRODUCT_ID,

  internalApps: {
    blogifyAPI: {
      url: process.env.BASE_URL,
    },
    blogifyClient: {
      url: process.env.DASHBOARD_REDIRECT_URL,
    },
    blogifyAdmin: {
      url: process.env.ADMIN_URL,
    },
    blogifyMedia: {
      url: process.env.BLOGIFY_MEDIA_SERVICE_URL,
      apiKey: process.env.BLOGIFY_MEDIA_API_KEY,
    },
    blogifyML: {
      url: process.env.BLOGIFY_ML_SERVICE_URL,
      apiKey: process.env.BLOGIFY_ML_API_KEY,
    },
  },

  logger: {
    sentry: process.env.SENTRY_DSN,
  },
  swagger: {
    enabled: true,
    favIcon: 'https://blogify.ai/favicon.ico?v=2',
    docs: [],
  },
  cache: {
    redis: getRedisConfig(),
  },
  aws: {
    access: process.env.AWS_ACCESS_KEY_ID,
    secret: process.env.AWS_SECRET_ACCESS_KEY,
    region: process.env.AWS_REGION,
    bucket: process.env.AWS_BUCKET_NAME,
    imageBucket: process.env.AWS_IMAGE_BUCKET_NAME,
  },
  mail: {
    mailChimp: {
      apiKey: process.env.MAIL_CHIMP_API_KEY,
    },
    brevo: {
      apiKey: process.env.BREVO_API_KEY,
    },
    registration: '<EMAIL>',
    support: '<EMAIL>',
    notify: '<EMAIL>',
    supportEmail: '<EMAIL>',
  },
  // Internal Integrations
  openAI: {
    apiKey: process.env.OPENAI_API_KEY,
  },
  assemblyAI: {
    serviceUrl: process.env.SERVICE_URL,
    token: process.env.ASSEMBLY_TOKEN,
  },
  deepgram: {
    token: process.env.DEEPGRAM_TOKEN,
  },
  slack: {
    signingSecret: process.env.SLACK_SIGNING_SECRET,
    botToken: process.env.SLACK_BOT_TOKEN,
    token: process.env.SLACK_WEBHOOK_TOKEN,
    channels: {
      signup: process.env.SLACK_WEBHOOK_CHANNEL_MONITOR_SIGNUP,
      paidSignup: process.env.SLACK_WEBHOOK_CHANNEL_PAID_PLAN_SIGNUP,
      addonSubscriptions: process.env.SLACK_WEBHOOK_CHANNEL_ADDON_SUBSCRIPTIONS,
    },
  },
  github: {
    authToken: process.env.GITHUB_AUTH_TOKEN,
  },
  vercel: {
    apiToken: process.env.VERCEL_API_TOKEN,
  },
  vapid: {
    privateKey: process.env.VAPID_PRIVATE_KEY,
    publicKey: process.env.VAPID_PUBLIC_KEY,
  },
  google: {
    clientId: process.env.GOOGLE_CLIENT_ID,
    clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    ads: {
      loginCustomerId: process.env.GOOGLE_ADS_LOGIN_CUSTOMER_ID,
      developerToken: process.env.GOOGLE_ADS_DEVELOPER_TOKEN,
    },
    vertex: {
      clientEmail: process.env.GOOGLE_VERTEX_CLIENT_EMAIL,
      privateKey: process.env.GOOGLE_VERTEX_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    },
  },
  // Payment Integration
  stripe: {
    secreteKey: process.env.STRIPE_SECRET_KEY,
    webhookSecret: process.env.STRIPE_WEBHOOK_SECRET,
    lifetimeToken: process.env.STRIPE_LIFETIME_TOKEN,
  },
  // Affiliate Network Integration
  impact: {
    accountSid: process.env.IMPACT_AFFILIATE_ACCOUNT_SID,
    authToken: process.env.IMPACT_AFFILIATE_AUTH_TOKEN,
  },
  shareASale: {
    merchantId: process.env.SHARE_A_SALE_MERCHANT_ID,
    apiSecret: process.env.SHARE_A_SALE_API_SECRET,
    apiToken: process.env.SHARE_A_SALE_API_TOKEN,
  },
  // Blog & Site Integrations
  mailchimp: {
    clientId: process.env.MAILCHIMP_CLIENT_ID,
    clientSecret: process.env.MAILCHIMP_CLIENT_SECRET,
  },
  wordpress: {
    clientId: process.env.WORDPRESS_CLIENT_ID,
    clientSecret: process.env.WORDPRESS_CLIENT_SECRET,
  },
  blogger: {
    clientId: process.env.BLOGGER_CLIENT_ID,
    clientSecret: process.env.BLOGGER_CLIENT_SECRET,
  },
  wix: {
    clientId: process.env.WIX_CLIENT_ID,
    clientSecret: process.env.WIX_CLIENT_SECRET,
  },
  spotify: {
    clientId: process.env.SPOTIFY_CLIENT_ID,
    clientSecret: process.env.SPOTIFY_CLIENT_SECRET,
  },
  // Social Integrations
  facebook: {
    clientId: process.env.FACEBOOK_CLIENT_ID,
    clientSecret: process.env.FACEBOOK_CLIENT_SECRET,
  },
  twitter: {
    clientId: process.env.TWITTER_CLIENT_ID,
    clientSecret: process.env.TWITTER_CLIENT_SECRET,
    appId: parseInt(process.env.TWITTER_APP_ID, 10),
    apiKey: process.env.TWITTER_API_KEY,
    apiSecret: process.env.TWITTER_API_SECRET,
    token: process.env.TWITTER_BEARER_TOKEN,
  },
  linkedIn: {
    clientId: process.env.LINKEDIN_CLIENT_ID,
    clientSecret: process.env.LINKEDIN_CLIENT_SECRET,
  },
  blockedCountries: process.env.BLOCKED_COUNTRIES,
  blockedIpAddresses: process.env.BLOCKED_IP_ADDRESSES,

  // Feature Flags
  featureFlags: {
    isReviewRequired: process.env.IS_REVIEW_REQUIRED === 'true',
  },
};

export default (): Config => config;
