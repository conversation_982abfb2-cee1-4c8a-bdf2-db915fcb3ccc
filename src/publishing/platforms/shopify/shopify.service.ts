// بسم الله الرحمن الرحيم

import { Business } from '@/business/business.model';
import type { PlatformService, PublishResult, Site } from '@/publishing/interfaces/platforms';
import type { StoreName } from '@/publishing/platforms/shopify/utils';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import type { ObjectId } from 'mongodb';
import type { Model, Types } from 'mongoose';
import { articlePage, blogSite, endpoint } from './utils';
import axios, { AxiosError } from 'axios';

@Injectable()
export class ShopifyService implements PlatformService<'shopify'> {
  constructor(@InjectModel(Business.name) private readonly business: Model<Business>) {}

  async fetchShopDetails(shop: StoreName, accessToken: string): Promise<Site> {
    const query = `
          query {
            shop {
              id
              name
              primaryDomain {
                url
              }
            }
          }
        `;

    try {
      const { data } = await axios.post(
        endpoint(shop),
        { query },
        {
          headers: {
            'Content-Type': 'application/json',
            'X-Shopify-Access-Token': accessToken,
          },
        },
      );

      return {
        id: shop,
        name: data.data.shop.name,
        url: data.data.shop.primaryDomain.url,
      };
    } catch (error) {
      throw error instanceof AxiosError ? error : new Error(String(error));
    }
  }

  async fetchBlogs(shop: StoreName, accessToken: string): Promise<Site[]> {
    const query = `
          query BlogList {
            blogs(first: 50) {
              nodes {
                id
                handle
                title
              }
            }
          }
        `;

    try {
      const { data } = await axios.post(
        endpoint(shop),
        { query },
        {
          headers: {
            'Content-Type': 'application/json',
            'X-Shopify-Access-Token': accessToken,
          },
        },
      );

      return data.data.blogs.nodes.map((blog) => ({
        id: blog.id,
        name: blog.title,
        url: blogSite(shop, blog.handle),
      }));
    } catch (error) {
      throw error instanceof AxiosError ? error : new Error(String(error));
    }
  }

  async fetchAuthors(shop: StoreName, accessToken: string): Promise<string[]> {
    const query = `
          query AuthorList {
            articleAuthors(first: 50) {
              nodes {
                name
              }
            }
          }
        `;

    try {
      const { data } = await axios.post(
        endpoint(shop),
        { query },
        {
          headers: {
            'Content-Type': 'application/json',
            'X-Shopify-Access-Token': accessToken,
          },
        },
      );

      return data.data.articleAuthors.nodes.map((author) => author.name);
    } catch (error) {
      throw error instanceof AxiosError ? error : new Error(String(error));
    }
  }

  async fetchSites(bid: Types.ObjectId): Promise<Site[]> {
    const tokens = await this.getTokens(bid);
    const [shop, accessToken] = Object.entries(tokens)[0] || [];

    return await this.fetchBlogs(shop, accessToken);
  }

  async publish({
    bid,
    title,
    content,
    site,
    image,
    draft,
    tldr,
    keywords,
  }: {
    blogID: Types.ObjectId;
    bid: Types.ObjectId;
    title: string;
    content: string;
    image?: URL;
    tldr: string;
    draft: boolean;
    keywords: string[];
    metaTags: string[];
    metaDescription: string;
  } & { site: string }): Promise<PublishResult> {
    try {
      const tokens = await this.getTokens(bid);
      const [shop, accessToken] = Object.entries(tokens)[0];

      const mutation = `
            mutation CreateArticle($article: ArticleCreateInput!) {
                articleCreate(article: $article) {
                    article {
                        handle
                        blog {
                            handle
                        }
                    }
                        userErrors {
                        code
                        field
                        message
                    }
                }
            }
            `;

      const variables = {
        article: {
          author: { name: 'Blogify' },
          blogId: site,
          body: content,
          isPublished: !draft,
          ...(image && { image: { altText: title, src: image } }),
          summary: tldr,
          tags: keywords,
          title,
        },
      };

      const { data } = await axios.post(
        endpoint(shop),
        { query: mutation, variables },
        {
          headers: {
            'Content-Type': 'application/json',
            'X-Shopify-Access-Token': accessToken,
          },
        },
      );

      if (data.data.articleCreate.userErrors.length > 0) {
        throw new Error(data.data.articleCreate.userErrors[0].message);
      }

      return {
        kind: 'success',
        link: new URL(
          articlePage({
            shop,
            blogHandle: data.data.articleCreate.article.blog.handle,
            articleHandle: data.data.articleCreate.article.handle,
          }),
        ),
      };
    } catch (error) {
      return {
        kind: 'failure',
        error: new Error(
          `Shopify publish error: ${error instanceof Error ? error.message : String(error)}`,
        ),
      };
    }
  }

  async disconnect(bid: Types.ObjectId): Promise<void> {
    await this.business.findByIdAndUpdate(bid, {
      shopifyAccessTokens: {},
    });
  }

  async isConnected(_bid: Types.ObjectId): Promise<boolean> {
    return false;
    // return this.getTokens(bid).then((tokens) => Object.keys(tokens).length > 0);
  }

  async saveToken(businessID: ObjectId, shop: string, accessToken: string) {
    await this.business.findByIdAndUpdate(businessID, {
      [`shopifyAccessTokens.${shop}`]: accessToken,
    });
  }

  async getTokens(businessID: ObjectId) {
    const { shopifyAccessTokens } = await this.business.findById(businessID);
    return shopifyAccessTokens;
  }
}
