// بسم الله الرحمن الرحيم

import type {
  PlatformService,
  PublishPayload,
  PublishResult,
  Site,
} from '@/publishing/interfaces/platforms';
import { Business } from '@business/business.model';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { google } from 'googleapis';
import { ObjectId } from 'mongodb';
import mongoose, { type Model } from 'mongoose';

@Injectable()
export class BloggerService implements PlatformService<'blogger'> {
  constructor(
    private config: ConfigService,
    @InjectModel(Business.name) private readonly business: Model<Business>,
  ) {}

  async updateBusinessBloggerToken(
    businessId: mongoose.Types.ObjectId,
    bloggerId: string,
    tokens: Business['bloggerTokens'][string],
  ): Promise<Business> {
    const business = await this.business.findByIdAndUpdate(
      businessId,
      { [`bloggerTokens.${bloggerId}`]: tokens },
      { new: true },
    );
    if (!business) {
      throw new Error(`Business with ID ${businessId} not found`);
    }
    return business;
  }

  private getSDK = () =>
    new google.auth.OAuth2(
      this.config.get('BLOGGER_CLIENT_ID'),
      this.config.get('BLOGGER_CLIENT_SECRET'),
      this.config.get('BASE_URL') + '/blogger/callback',
    );

  private async getClient(businessID: mongoose.Types.ObjectId, accountID: string) {
    const { bloggerTokens } = await this.business.findById(businessID);
    let tokens = bloggerTokens[accountID];
    if (new Date().getTime() >= tokens.expiry_date) {
      const newTokens = await this.refreshAccessToken(tokens.access_token, tokens.refresh_token);
      await this.updateBusinessBloggerToken(businessID, accountID, newTokens);
      tokens = newTokens;
    }
    const sdk = this.getSDK();
    sdk.setCredentials({ access_token: tokens.access_token });
    return google.blogger({ version: 'v3', auth: sdk });
  }

  async isConnected(bid: mongoose.Types.ObjectId): Promise<boolean> {
    const business = await this.business.findById(bid);
    return (
      Object.keys(business.bloggerTokens).length > 0 && (await this.fetchSites(bid)).length > 0
    );
  }

  async disconnect(bid: mongoose.Types.ObjectId): Promise<void> {
    await this.business.findByIdAndUpdate(bid, { bloggerTokens: {} }, { new: true });
  }

  async fetchSites(bid: mongoose.Types.ObjectId): Promise<Site[]> {
    return Object.values(await this.fetchBlogs(bid)).flatMap(({ blogs }) => blogs);
  }

  getConsentScreenURL = (businessID: mongoose.Types.ObjectId) =>
    this.getSDK().generateAuthUrl({
      access_type: 'offline',
      prompt: 'consent',
      scope: ['https://www.googleapis.com/auth/blogger'],
      state: businessID.toString(),
    });

  async saveTokens(code: string, state: string) {
    const {
      tokens: { access_token, refresh_token, expiry_date },
    } = await this.getSDK().getToken(code);
    const sdk = this.getSDK();
    sdk.setCredentials({ access_token });
    const client = google.blogger({ version: 'v3', auth: sdk });
    const {
      data: { id },
    } = await client.users.get({ userId: 'self' });
    await this.updateBusinessBloggerToken(new ObjectId(state), id, {
      access_token,
      refresh_token,
      expiry_date,
    });
    return this.config.get('DASHBOARD_REDIRECT_URL') + '/dashboard/settings';
  }

  async refreshAccessToken(
    accessToken: string,
    refreshToken: string,
  ): Promise<Business['bloggerTokens'][string]> {
    return new Promise((resolve, reject) => {
      const sdk = this.getSDK();
      sdk.setCredentials({
        access_token: accessToken,
        refresh_token: refreshToken,
      });
      sdk.refreshAccessToken((err, tokens) => {
        if (err) {
          return reject(err);
        }
        resolve({
          access_token: tokens.access_token,
          refresh_token: tokens.refresh_token,
          expiry_date: tokens.expiry_date,
        });
      });
    });
  }

  async fetchBlogs(bid: mongoose.Types.ObjectId) {
    try {
      const business = await this.business.findOne(bid);
      return Promise.all(
        Object.entries(business.bloggerTokens).map(async ([userId, tokens]) => {
          const blogger = await this.getClient(bid, userId);
          const response = await blogger.blogs.listByUser({
            userId, // get the list of blogs for the authenticated user
          });

          const blogs =
            response.data?.items?.map((site) => ({
              name: site.name,
              url: site.url,
              id: site.id,
            })) ?? [];
          return <[string, { tokens: Business['bloggerTokens'][string]; blogs: Site[] }]>[
            userId,
            { tokens, blogs },
          ];
        }),
      ).then((entries) => Object.fromEntries(entries));
    } catch (error) {
      throw new Error(`Failed to fetch blogger sites: ${error.message}`);
    }
  }

  /**
   * Blogger requires the sum of keywords to be at most 120,
   * so this function returns an array of keywords which satisfies
   * this constraint
   *
   */
  trim_keywords(keywords?: string[]): string[] {
    if (!keywords) return [];
    const trimmed_keywords = [];
    for (let i = 0, total_length = 0; i < keywords.length; i++) {
      if (keywords[i].length + total_length < 120) {
        trimmed_keywords.push(keywords[i]);
        total_length += keywords[i].length;
      }
    }
    return trimmed_keywords;
  }

  async publish({
    bid,
    title,
    content,
    draft,
    image,
    keywords,
    tldr,
    site: siteID,
  }: PublishPayload): Promise<PublishResult> {
    const info = await this.fetchBlogs(bid);
    const lookupToken = Object.fromEntries(
      Object.values(info).flatMap(({ tokens, blogs }) =>
        blogs.map(({ id }): [string, typeof tokens] => [id, tokens]),
      ),
    );
    try {
      const tokens = lookupToken[siteID];
      const sdk = this.getSDK();
      sdk.setCredentials({
        access_token: tokens.access_token,
        refresh_token: tokens.refresh_token,
      });
      const blogger = google.blogger({ version: 'v3', auth: sdk });
      // Docs: https://developers.google.com/blogger/docs/3.0/reference/posts/insert
      const response = await blogger.posts.insert({
        blogId: siteID,
        isDraft: draft,
        requestBody: {
          customMetaData: JSON.stringify({
            metaTags: this.trim_keywords(keywords),
            metaDescription: tldr,
          }),
          title: title,
          content: `<div class="separator" style="clear: both;">
                <a href="${image}" style="display: block; padding: 1em 0px; text-align: center;">
            <img alt="${title}" border="0" height="320" src="${image}" />
                </a>
              </div>
              <div>
                ${content}
              </div>`,
          labels: this.trim_keywords(keywords),
        },
      });
      return { link: new URL(response?.data?.url), kind: 'success' };
    } catch (error) {
      return { error: new Error(error), kind: 'failure' };
    }
  }
}
