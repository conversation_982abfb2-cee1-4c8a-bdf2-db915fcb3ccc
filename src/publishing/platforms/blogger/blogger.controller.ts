// بسم الله الرحمن الرحيم

import { Controller, Get, Query, Res, UseGuards } from '@nestjs/common';
import { Response as ExpressResponse } from 'express';
import { BloggerService } from './blogger.service';
import mongoose from 'mongoose';
import { QueryTokenGuard } from '@/auth/guards/query-token.guard';
import { AuthInfo } from '@/auth/decorators/jwt.decorator';
import { Auth } from '@/auth/guards/auth.guard';

@Controller('blogger')
export class BloggerController {
  constructor(private readonly bloggerService: BloggerService) {}

  @Get('connect')
  @UseGuards(QueryTokenGuard, Auth)
  async connect(
    @AuthInfo('bid') businessID: mongoose.Types.ObjectId,
    @Res({ passthrough: true }) response: ExpressResponse,
  ) {
    response.redirect(this.bloggerService.getConsentScreenURL(businessID));
  }

  @Get('callback')
  async callback(
    @Query('code') code: string,
    @Query('state') state: string,
    @Res({ passthrough: true }) response: ExpressResponse,
  ) {
    if (mongoose.Types.ObjectId.isValid(state)) {
      response.redirect(await this.bloggerService.saveTokens(code, state));
    }
  }
}
