// بسم الله الرحمن الرحيم

import { Module } from '@nestjs/common';
import { BloggerController } from './blogger.controller';
import { BloggerService } from './blogger.service';
import { BusinessModule } from '@business/business.module';

/**
 * Represents the Blogger Integration which used to publish articles to blogger.
 * App: https://console.cloud.google.com/auth/clients/714899215594-3a4ltlahq8jabhefcbrc5he299p88g8p.apps.googleusercontent.com?inv=1&invt=AbykaQ&project=pixel-shadow-blogify
 * Envs:
 *  - BLOGGER_CLIENT_ID
 *  - BLOGGER_CLIENT_SECRET
 */
@Module({
  controllers: [BloggerController],
  imports: [BusinessModule],
  providers: [BloggerService],
  exports: [BloggerService],
})
export class BloggerModule {}
