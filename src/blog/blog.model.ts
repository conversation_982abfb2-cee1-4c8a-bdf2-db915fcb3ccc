import type {
  BlogAffiliateLinkGenerationStatus,
  BlogSeoAnalysisStatus,
  SocialPlatform,
  BlogPlatform,
  BlogHeading,
  BlogStatus,
} from './blog.type';
import type { YouTubeTranscript } from '@/youtube/youtube-content/youtube-content.interface';
import type { GenerateImageDto } from '@/resources/image/dto/generate-image.dto';
import type { Platform } from '@/publishing/interfaces/platforms';
import type { Tag } from '@/seo/functions/keywords';

import { SchemaFactory, Schema, Prop } from '@nestjs/mongoose';
import { ApiHideProperty } from '@nestjs/swagger';
import { Document } from 'mongoose';
import { ObjectId } from 'mongodb';

import { replaceUrlSlug } from '@/common/utils/url';

import {
  BLOG_GENERATION_MODES,
  BLOG_PUBLISH_STATUS,
  BLOG_SOURCE_NAMES,
  BLOG_SOURCE_TYPES,
  BLOG_SIZES,
  BLOG_TONES,
  BLOG_POV,
} from './blog.constants';
import {
  BlogCoverImageType,
  BlogGenerationMode,
  BlogPublishStatus,
  BlogTldrPosition,
  BlogSourceName,
  BlogSourceType,
  BlogSize,
  BlogTone,
  BlogPov,
} from './blog.enums';
import { attachIdsToBlogHeadings, getTableOfContents } from './blog-content.utils';
import { countWords } from './blog.utils';

export enum BlogType {
  Text = 'blog-from-text',
  Media = 'blog-from-media',
  // Rephrase = 'blog-rephrase'
}

type BlogOutline = {
  title: string;
  summary?: string;
  targetAudience?: string;
  introduction: string;
  tldr: string;
  conclusion: string;
  metaDescription: string;
  metaTags: string[];
  keywords?: string[];
  sections: {
    heading: string;
    bulletPoints: string[];
    notes: string;
    keywords: string[];
    metaDescription: string;
    data: string[];
    quotes: string[];
    relevantContent: string;
    targetedKeywords?: string[];
    keyFindings?: string[];
    captionIndexes?: {
      startCaptionIndex: number;
      endCaptionIndex: number;
    }[];
    svgPrompt?: string;
    shouldGenerateTable?: boolean;
    shouldGenerateChart?: boolean;
  }[];
};

// Interface for the keyword item structure
export interface KeywordItem {
  keyword: string;
  min: number;
  max: number;
}

// Interface for the overall SEO keywords structure
export interface SeoKeywords {
  title: KeywordItem[];
  meta: KeywordItem[];
  h1: KeywordItem[];
  h2: KeywordItem[];
  h3: KeywordItem[];
  content: KeywordItem[];
  citations: string[];
  keyFindings: string[];
}
export interface AffiliateKeywordMatch {
  keyword: string;
  beforeWord: string;
  afterWord: string;
  confidence?: number;
}

export interface BlogAffiliateKeyword extends AffiliateKeywordMatch {
  productName?: string;
  productId?: string;
  productCatalogId?: string;
  productCampaignId?: string;
  productCatalogItemId?: string;
  productImageUrl?: string;
  productCurrentPrice?: number;
  productOriginalPrice?: number;
  productCurrency?: string;
  productCategory?: string;
  productItemGroupId?: string;
  affiliateLink?: string;
  searchTerms?: string[];
  status: 'matched' | 'failed' | 'pending' | 'no_match';
  matchedAt?: Date;
  replacementCount?: number;
}

export enum SeoOption {
  ACTIVE = 'ACTIVE', // Run seo analysis and use the seo keywords to improve score during the generation
  PASSIVE = 'PASSIVE', // Passively run the seo analysis and populate results to get score
}

@Schema({ timestamps: true, versionKey: false })
export class Blog extends Document {
  /* User Inputs */
  // Source Info
  @Prop({ type: String })
  url: string;

  @Prop({ type: String })
  prompt: string;

  @Prop({ type: String })
  customInstruction?: string;

  // TODO: Remove 'media', 'text', 'web' Later
  @Prop({ type: String, enum: [...BLOG_SOURCE_TYPES, 'media', 'text', 'web'] })
  sourceType: BlogSourceType;

  @Prop({ type: String, enum: BLOG_SOURCE_NAMES })
  sourceName: BlogSourceName;
  // End Source Info

  // Blog Settings
  @Prop({ type: String, default: BlogGenerationMode.Auto, enum: BLOG_GENERATION_MODES })
  generationMode: BlogGenerationMode;

  @Prop({ type: String, default: BlogPov.FirstPerson, enum: BLOG_POV })
  pov: BlogPov;

  @Prop({ type: String, default: BlogTone.Neutral, enum: BLOG_TONES })
  blogTone: BlogTone;

  @Prop({ type: String, default: BlogSize.medium, enum: BLOG_SIZES })
  blogSize: BlogSize;

  @Prop({ type: String, default: 'Global English' })
  inputLanguage: string;

  @Prop({ type: String, default: 'english' })
  blogLanguage: string;

  @Prop({ type: String, default: 'No Table' })
  tableOption: string;

  @Prop({ type: String, default: 'No Chart' })
  chartOption: string;

  @Prop({ type: Boolean, default: true })
  includeQuotation?: boolean;

  @Prop({ type: Boolean, default: false })
  embedSource?: boolean;

  @Prop({ type: Boolean, default: false })
  embedSourceAsCover?: boolean;

  @Prop({ type: String, default: BlogTldrPosition.end, enum: Object.values(BlogTldrPosition) })
  tldrPosition: BlogTldrPosition;

  @Prop({ type: Boolean, default: false })
  affiliateCommissionOptIn?: boolean;

  @Prop({ type: [String], default: [] })
  authorsToGiveCredit: string[];

  @Prop({ type: String, enum: SeoOption, default: SeoOption.ACTIVE })
  seoOption: string;

  @Prop({ type: [String] })
  seoInputKeywords?: string[];

  @Prop({ type: Number })
  wordCountApprox?: number;

  @Prop({ type: Number, min: 0, max: 100 })
  sourceSimilarity?: number;

  @Prop({ type: Boolean, default: false })
  seoOptimization?: boolean;

  @Prop({ type: String })
  affiliateTargetLocation?: string;
  // End Blog Settings

  // Images
  @Prop({ type: String })
  image: string;

  @Prop({ type: String })
  thumbnail?: string;

  @Prop({ type: String, enum: Object.values(BlogCoverImageType), nullable: true })
  coverImageType?: BlogCoverImageType;

  @Prop({ type: Object })
  aiGeneratedCoverImageConfig?: Omit<GenerateImageDto, 'prompt'>;

  @Prop({ type: Array, default: [] })
  contentImages: string[];

  @Prop({ type: Object })
  aiGeneratedContentImagesConfig?: Omit<GenerateImageDto, 'prompt'> & { count: number };
  // End Images

  // Publish Settings
  @Prop({ type: Array, default: [] })
  platforms: BlogPlatform[];

  @Prop({
    type: Object,
    default: {
      bad: 0,
      ok: 0,
      nice: 0,
      great: 0,
      awesome: 0,
    },
  })
  publishRatings: Record<'bad' | 'ok' | 'nice' | 'great' | 'awesome', number>;

  @Prop({ type: [Object], default: [] })
  publishResult?: {
    integration: string | Platform;
    siteID?: string;
    visibility?: 'visible' | 'hidden';
    outcome: { link: string; time: string } | { error: string; time: string };
  }[];

  @Prop({ type: Array, default: [] })
  socials: SocialPlatform[];
  // End Publish Settings
  /* End User Inputs */

  // Blog Contents
  @ApiHideProperty()
  @Prop({ type: Object, default: {} })
  blogOutline?: BlogOutline;

  @ApiHideProperty()
  @Prop({ type: [Object] })
  seoKeywords?: Array<{ url: string } & Record<Tag, string[]>>;

  @ApiHideProperty()
  @Prop({ type: Object })
  webSearchSeoKeywords?: SeoKeywords;

  @ApiHideProperty()
  @Prop({ type: Object })
  legacySeoKeywords?: SeoKeywords;

  @ApiHideProperty()
  @Prop({ type: Object })
  seoResults: {
    rangeChart: Record<
      Tag,
      Record<
        string /** Keyword */,
        [min: number /** Minimum frequency */, max: number /** Maximum Frequency */]
      >
    >;
  };

  @ApiHideProperty()
  @Prop({ type: Number, min: 0, max: 100 })
  seoScore?: number;

  @ApiHideProperty()
  @Prop({ type: Object })
  seoAnalysisResults?: {
    overallScore: number;
    totalKeywords: number;
    successfulKeywords: number;
    sectionBreakdown: Array<{
      section: string;
      score: number;
      keywordsChecked: number;
      keywordsOptimized: number;
    }>;
    keyFindings: string[];
    citations: string[];
    timestamp: Date;
  };

  @ApiHideProperty()
  @Prop({ type: Object, default: {} })
  _blogOutline?: BlogOutline;

  @Prop({ type: String, default: '' })
  title: string;

  @Prop({ type: String, unique: true })
  slug: string;

  @ApiHideProperty()
  @Prop({ type: String, default: '' })
  transcription: string;

  @ApiHideProperty()
  @Prop({ type: [Object] })
  transcript: Array<YouTubeTranscript>;

  @ApiHideProperty()
  @Prop({ type: String })
  transcriptionSummary?: string;

  @ApiHideProperty()
  @Prop({ type: Object })
  contentImagesMapping?: object;

  @Prop({ type: String })
  content: string;

  @Prop({ type: Number })
  wordCount: number;

  @Prop({ type: Array, default: [] })
  keywords: string[];

  @Prop({ type: [Object], default: [] })
  tableOfContents: BlogHeading[];

  @Prop({
    type: [
      {
        keyword: String,
        beforeWord: String,
        afterWord: String,
        confidence: Number,
        productName: String,
        productId: String,
        productCatalogId: String,
        productCampaignId: String,
        productCatalogItemId: String,
        productImageUrl: String,
        productCurrentPrice: Number,
        productOriginalPrice: Number,
        productCurrency: String,
        productCategory: String,
        productItemGroupId: String,
        affiliateLink: String,
        searchTerms: [String],
        status: {
          type: String,
          enum: ['matched', 'failed', 'pending', 'no_match'],
        },
        matchedAt: Date,
        replacementCount: Number,
      },
    ],
    default: [],
  })
  affiliateKeywords: BlogAffiliateKeyword[];

  @ApiHideProperty()
  @Prop({ type: Date, default: new Date() })
  affiliateKeywordsUpdatedAt?: Date;

  @ApiHideProperty()
  @Prop({ type: String })
  metaDescription?: string;

  @Prop({ type: String })
  socialContent?: string;

  @Prop({ type: Date })
  publishAt: Date;

  @ApiHideProperty()
  @Prop({ type: Date })
  publishTime: Date;

  @Prop({ type: [{ type: ObjectId, ref: 'BlogCategory' }], default: [] })
  categories: ObjectId[];
  // End Blog Contents

  // Identifiers
  @ApiHideProperty()
  @Prop({ type: String })
  bid: string;

  @ApiHideProperty()
  @Prop({ type: ObjectId, ref: 'User' })
  uid: string;

  @ApiHideProperty()
  @Prop()
  country?: string;

  @Prop({ type: String })
  identifier: string;

  @Prop({ type: Boolean, default: false })
  isPartOfBulkGeneration: boolean;

  @ApiHideProperty()
  @Prop({ type: String })
  shopifyProductId?: string;
  // End Identifiers

  // Feedback
  @ApiHideProperty()
  @Prop({ type: Number, max: 5, min: 0, default: 0 })
  rating?: number;

  @ApiHideProperty()
  @Prop({ type: String })
  ratingFeedback?: string;
  // End Feedback

  // Statuses and Error Reporting & Handling
  @Prop({ type: String, default: 'queued' })
  status: BlogStatus;

  @Prop({ type: String })
  seoAnalysisStatus?: BlogSeoAnalysisStatus;

  @Prop({ type: String })
  affiliateLinkGenerationStatus?: BlogAffiliateLinkGenerationStatus;

  @Prop({ type: String, enum: BLOG_PUBLISH_STATUS, default: 'draft' })
  publishStatus: BlogPublishStatus;

  @Prop({ type: Boolean, default: false })
  isReviewed: boolean;

  @Prop({ type: String, default: 'started' })
  generationStatus?: string;

  @Prop({ type: Number, default: 0 })
  totalRetries: number;

  @Prop({ type: String, default: '' })
  failReason: string;

  @Prop({ type: String })
  failedQueue: string;

  @Prop({ type: String })
  errorCode: string;

  @Prop({ type: String })
  slackAlertTs?: string;

  // Cost tracking - simple number representation of cost in USD
  @ApiHideProperty()
  @Prop({ type: Number })
  generationCost?: number;
  @ApiHideProperty()
  @Prop({ type: [Object] })
  generationCostBreakdown?: {
    step: string;
    cost: number;
    model: string;
    timestamp: Date;
  }[];
  // End Statuses and Error Reporting & Handling

  /* Publishing */
  // Blog Publishing
  // Blogify
  @ApiHideProperty()
  @Prop({ type: String, default: '' })
  blogifyLink: string;

  @ApiHideProperty()
  @Prop({ type: Date })
  blogifyPublishTime: Date;

  // Blogger
  @ApiHideProperty()
  @Prop({ type: String, default: '' })
  bloggerLink: string;

  @ApiHideProperty()
  @Prop({ type: Date })
  bloggerPublishTime: Date;

  // Wordpress
  @ApiHideProperty()
  @Prop({ type: String, default: '' })
  wordpressLink: string;

  @ApiHideProperty()
  @Prop({ type: Date })
  wordpressPublishTime: Date;

  // Wordpress Org
  @ApiHideProperty()
  @Prop({ type: String, default: '' })
  wordpressorgLink: string;

  @ApiHideProperty()
  @Prop({ type: Date })
  wordpressorgPublishTime: Date;

  // Medium
  @ApiHideProperty()
  @Prop({ type: String, default: '' })
  mediumLink: string;

  @ApiHideProperty()
  @Prop({ type: Date })
  mediumPublishTime: Date;

  // Mailchimp
  @ApiHideProperty()
  @Prop({ type: String, default: '' })
  mailchimpLink: string;

  @ApiHideProperty()
  @Prop({ type: Date })
  mailchimpPublishTime: Date;

  // Wix
  @ApiHideProperty()
  @Prop({ type: String, default: '' })
  wixLink: string;

  @ApiHideProperty()
  @Prop({ type: Date })
  wixPublishTime: Date;

  // Zapier
  @ApiHideProperty()
  @Prop({ type: String, default: '' })
  zapierLink: string;

  @ApiHideProperty()
  @Prop({ type: Date })
  zapierPublishTime: Date;

  @Prop({
    type: Object,
    default: {
      text: '',
      link: '',
      bgColor: '',
      borderRadius: 0,
    },
  })
  cta: {
    text: string;
    link: string;
    bgColor: string;
    borderRadius: number;
  };

  /* End Publishing */
}

export type BlogDocument = Blog & Document;

export type BlogQueuePayload = Pick<
  Blog,
  | 'bid'
  | 'uid'
  | 'country'
  | 'sourceType'
  | 'sourceName'
  | 'image'
  | 'coverImageType'
  | 'aiGeneratedCoverImageConfig'
  | 'contentImages'
  | 'aiGeneratedContentImagesConfig'
  | 'inputLanguage'
  | 'blogLanguage'
  | 'blogTone'
  | 'blogSize'
  | 'url'
  | 'prompt'
  | 'customInstruction'
  | 'title'
  | 'content'
  | 'transcription'
  | 'transcript'
  | 'transcriptionSummary'
  | 'blogOutline'
  | 'platforms'
  | 'socials'
  | 'generationMode'
  | 'tldrPosition'
  | 'embedSource'
  | 'affiliateCommissionOptIn'
  | 'authorsToGiveCredit'
  | 'keywords'
  | 'affiliateKeywords'
  | 'pov'
  | 'tableOption'
  | 'chartOption'
  | 'seoOption'
  | 'cta'
  | 'includeQuotation'
> & {
  // TODO: Remove Later and use Blog Instead
  // Queue Specific Fields
  blogId: string;
  email: string;
  blogTitle: string;
  blogContent: string;
  blogKeywords: string[];
  platform: string;
  siteIDs?: Array<string>;
  affiliateLinksGeneration: string;
  shouldGenerateAffiliateLinks: string;
  timestamp: string;
  draft: boolean;
  link: string;
  identifier?: string;
  creditTransactionId: string;
  wordCountApprox: number;
  sourceSimilarity: number;
  seoInputKeywords: string[];
  seoOptimization: boolean;
  affiliateTargetLocation: string;
  seoOptimizationData: object[];
  seoOptimizationDone: boolean;
  useLegacyScraping?: boolean;
  structuredSeoData?: SeoKeywords; // Contains either webSearchSeoKeywords or legacySeoKeywords
};

const slugify = (title: string): string =>
  title
    .normalize('NFKC') // Normalize Unicode characters
    .trim()
    .replace(/[^\p{L}\p{N}\s-]/gu, '') // Remove special characters but keep letters, numbers, spaces, and hyphens
    .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with a hyphen
    .replace(/^-+|-+$/g, '') // Remove leading and trailing hyphens
    .replace(/[A-Z]/g, (c) => c.toLowerCase()); // Convert only English uppercase letters to lowercase

const BlogSchema = SchemaFactory.createForClass(Blog);
BlogSchema.index({ title: 'text', content: 'text' });
BlogSchema.pre('save', async function (next) {
  const blog = this as BlogDocument;
  if (blog.isModified('content') && !!this.content?.length) {
    blog.wordCount = countWords(this.content);
    blog.content = attachIdsToBlogHeadings(this.content);
    blog.tableOfContents = getTableOfContents(blog.content);
  }

  if (this.isModified('title') && !!this.title?.length) {
    const baseSlug = slugify(this.title);
    let uniqueSlug = baseSlug;
    let counter = 1;

    // Check for existing slugs and increment the counter if there's a duplicate
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore: For now.
    while (await this.constructor.findOne({ slug: uniqueSlug })) {
      uniqueSlug = `${baseSlug}-${counter}`;
      counter++;
    }

    this.slug = uniqueSlug;

    // Update Link for Already Published Blog
    const publishResultIndex = this.publishResult.findIndex(
      (pr: Blog['publishResult'][number]) => pr.integration === 'blogify' && 'link' in pr.outcome,
    );
    if (publishResultIndex >= 0) {
      const result = this.publishResult[publishResultIndex];
      this.publishResult[publishResultIndex] = {
        ...result,
        outcome: {
          ...result.outcome,
          link: replaceUrlSlug(result.outcome['link'], uniqueSlug),
        },
      };
    }
  }

  next();
});

export { BlogSchema };
