import type { BlogQueuePayload, Blog } from '@blog/blog.model';
import type { YouTubeTranscript } from '@/youtube/youtube-content/youtube-content.interface';
import type { Queue, Job } from 'bull';

import { OnQueueCompleted, OnQueueFailed, InjectQueue, Processor, Process } from '@nestjs/bull';
import { ConfigService } from '@nestjs/config';
import { Logger } from '@nestjs/common';

import { CreditTransactionService } from '@/resources/credit-transaction/credit-transaction.service';
import { JOB_OPTIONS, JOB_QUEUES } from '@/common/constants';
import { BlogGenerationService } from '@/blog-generation/blog-generation.service';
import { getPlanModel, TASK } from '@/llm/llm.models';
import { BusinessService } from '@/business/business.service';
import { GatewayService } from '@/modules/gateway/gateway.service';
import { BaseProcessor } from '@/common/queue/base.processor';
import { OpenaiService } from '@/openai/openai.service';
import { ImageService } from '@/resources/image/image.service';
import { SlackService } from '@/integrations/internal/slack/slack.service';
import { BlogType } from '@/blog/blog.model';
import { delay } from '@/common/helpers';

import { BlogCoverImageType, BlogTldrPosition, BlogSize, BlogTone } from './blog.enums';
import { BlogService } from './blog.service';
import { formatUrl } from './blog.utils';

const predefinedColors = [
  '#D1D8DC',
  '#00E2B1',
  '#CCD7E6',
  '#54D3DA',
  '#A2ACE0',
  '#41B8DD',
  '#FBD0B9',
];

const SINGLE_SHOT_BLOG_MAX_SIZE = 800;

@Processor(JOB_QUEUES.GENERATE_BLOG_CONTENT)
export class BlogGenerateContentProcessor extends BaseProcessor {
  protected readonly queueName = JOB_QUEUES.GENERATE_BLOG_CONTENT;
  protected readonly logger = new Logger(BlogGenerateContentProcessor.name);

  constructor(
    @InjectQueue(JOB_QUEUES.GENERATE_BLOG_KEYWORDS) private generateBlogKeywordsQueue: Queue,
    @InjectQueue(JOB_QUEUES.GENERATE_BLOG_CONTENT) queue: Queue,
    private readonly creditTransactionService: CreditTransactionService,
    private readonly blogGenerationService: BlogGenerationService,
    private readonly businessService: BusinessService,
    private readonly gatewayService: GatewayService,
    private readonly configService: ConfigService,
    private readonly openaiService: OpenaiService,
    private readonly imageService: ImageService,
    private readonly slackService: SlackService,
    private readonly blogService: BlogService,
  ) {
    super(queue);
  }

  @Process({ concurrency: 10 })
  protected async process(job: Job<BlogQueuePayload>): Promise<void> {
    // eslint-disable-next-line complexity
    return this.processWithLock(job, async (job) => {
      const prompt = job.data.prompt;
      const {
        uid,
        blogId,
        bid,
        email,
        title,
        url,
        blogTitle,
        identifier,
        transcription,
        transcript,
        transcriptionSummary,
        blogOutline,
        blogTone,
        blogSize,
        sourceType,
        wordCountApprox,
        blogLanguage = 'English',
        pov,
        customInstruction,
        tldrPosition,
        authorsToGiveCredit,
        coverImageType,
        aiGeneratedCoverImageConfig,
        aiGeneratedContentImagesConfig,
        cta,
        includeQuotation = true,
      } = job.data;
      let { contentImages } = job.data;

      if (!blogOutline) {
        throw new Error(`Failed to generate blog, outline missing.`);
      }

      const { subscriptionPlan } = await this.businessService.findOne(bid);
      const blogSizeToWordCount = {
        mini: 400,
        small: 800,
        medium: 1800,
        large: 2500,
        'x-large': 4000,
      };
      const blogWordCount = wordCountApprox || blogSizeToWordCount[blogSize];
      const eachSection = Math.floor(blogWordCount / blogOutline.sections.length);

      this.logger.log(
        {
          blogSize,
          blogLanguage,
          blogTone,
          blogId,
        },
        'Generating blog content',
      );
      await this.blogService.update(bid, blogId, { status: 'content_generating' });

      this.gatewayService.sendBlogStatusUpdate(uid, {
        _id: blogId,
        identifier,
        status: 'content_generating',
      });

      let blogContent = '';
      let contentTitle = blogTitle || title;

      // Generate Image using AI
      // Cover Image
      let image = '';
      if (aiGeneratedCoverImageConfig && coverImageType === BlogCoverImageType.AIGenerated) {
        try {
          const { url: imgUrl } = await this.imageService.generateAndUploadImage(
            { uid, bid },
            { prompt: contentTitle, ...aiGeneratedCoverImageConfig },
          );
          image = imgUrl;
        } catch (e) {
          this.logger.log(`Failed to generate cover image for blog ${blogId}:`, e.message);
        }
      }

      // Content Images
      contentImages = contentImages || [];
      if (aiGeneratedContentImagesConfig?.count && !contentImages.length) {
        try {
          const promises = [];
          for (let i = 0; i < aiGeneratedContentImagesConfig?.count; i++) {
            const promise = this.imageService.generateAndUploadImage(
              { uid, bid },
              { prompt: blogOutline?.sections?.[i]?.heading, ...aiGeneratedContentImagesConfig },
            );
            promises.push(promise);
          }

          await Promise.allSettled(promises).then((resp) => {
            resp.forEach((r, i) => {
              if (r.status === 'fulfilled' && r.value?.url) {
                contentImages.push(r.value?.url);
              } else if (r.status === 'rejected') {
                this.logger.log(
                  `Failed to generate content image ${i + 1} for blog ${blogId}:`,
                  r.reason,
                );
              }
            });
          });
        } catch (e) {
          this.logger.log(`Failed to generate content images for blog ${blogId}:`, e.message);
        }
      }
      // End Generate Image using AI

      const contentImagesMapping: Record<string, string> = this.generateBlogContentImagesMapping(
        blogOutline,
        contentImages,
      );
      const inputText = subscriptionPlan.includes(`LIFETIME`)
        ? transcriptionSummary || prompt
        : transcription || transcriptionSummary || prompt;
      try {
        blogContent = await this.generateBlogSections({
          job,
          blogId,
          bid,
          blogOutline,
          blogLanguage,
          eachSection,
          blogTone,
          pov,
          contentImagesMapping,
          blogSize,
          inputText,
          transcript,
          customInstruction,
          singleShotBlog: blogWordCount <= SINGLE_SHOT_BLOG_MAX_SIZE,
          includeQuotation,
        });

        blogContent = this.convertMarkdownBoldToHtml(blogContent);
      } catch (error) {
        this.logger.error({ err: error, blogId }, 'Failed to generate blog sections');
        throw error;
      }

      try {
        if (!contentTitle) {
          this.logger.log(`Generating blog title from content for blog ${blogId}`);
          contentTitle = await this.openaiService.generateBlogTitle(
            blogId,
            transcriptionSummary || prompt,
            blogLanguage,
          );
          this.logger.log(`Generated blog title from content for blog ${blogId}`);
        }
      } catch (error) {
        this.logger.error(`Blog title generation failed for blog ${blogId}`, error.message);
        await this.handleContentGenerationFailure(
          job,
          bid,
          blogId,
          'Failed to generate blog title: ' + error?.message,
        );
        throw error;
      }

      const tldrContent = blogOutline.tldr
        ? blogOutline.tldr.replace(/TL;DR: |TLDR:|TLDR|TL;DR|tl;dr|tldr/, '')
        : '';

      if (blogOutline.introduction) {
        blogContent = `<p>${blogOutline.introduction}</p><!--INTROEND-->
        ${
          blogOutline.tldr && tldrPosition === BlogTldrPosition.start
            ? `<p><b>TL;DR: </b>${tldrContent}</p>`
            : ''
        }
        ${blogContent}`;
      }

      blogContent = blogContent.replace(/```html/g, '').replace(/```/g, '');

      if (blogOutline.tldr && tldrPosition === BlogTldrPosition.end) {
        blogContent += `<p><b>TL;DR: </b>${tldrContent}</p>`;
      }

      if (!!cta?.text) {
        const colorCode = cta.bgColor.replace('#', '');
        const ctaButton = `
          <div data-type="cta-button" data-node-type="ctaButton"><a href="${formatUrl(
            cta.link,
          )}" target="_blank" rel="noopener noreferrer" style="background-color: #${colorCode}; border-radius: ${
            cta.borderRadius
          }px; color: #ffffff; padding: 10px 20px; margin: 10px 0; text-decoration: none;">${
            cta.text
          }</a></div>
        `;
        blogContent += ctaButton;
      }

      if (url && authorsToGiveCredit && authorsToGiveCredit.length) {
        try {
          const creditSection = await this.blogGenerationService.generateCreditSection({
            url,
            authorsToGiveCredit,
            language: blogLanguage,
          });
          blogContent += `<p>${creditSection}</p>`;
        } catch (e) {
          this.logger.error(`Failed to add credit section for blog ${blogId}`, e.message);
        }
      }

      try {
        const generationStatus = `Generated blog content, sending for review`;
        const status = 'content_generated';
        const updates: Partial<Blog> = {
          title: blogTitle,
          content: blogContent,
          contentImagesMapping,
          status,
          generationStatus,
        };
        if (image) {
          updates.image = image;
        }
        if (contentImages?.length) {
          updates.contentImages = contentImages;
        }

        const blog = await this.blogService.update(bid, blogId, updates);

        // Update Existing Slack Message
        await this.slackService.updateBlogCreateAlert(blog, blog.slackAlertTs, { bid, email });

        // Push New Message to Same Thread
        const blocks = [
          { type: 'section', text: { type: 'mrkdwn', text: 'Generated blog *content*.' } },
        ];
        await this.slackService.sendMessage({
          channel: 'monitor-blog-create',
          message: 'Generated blog content.',
          thread_ts: blog.slackAlertTs,
          blocks,
        });

        this.logger.log(`${generationStatus} for blog ${blogId} email ${email}`);
        this.gatewayService.sendBlogStatusUpdate(uid, { _id: blogId, identifier, status });

        await this.creditTransactionService.deductContentCost(
          { bid, uid },
          {
            contentType: ['audio', 'video'].includes(sourceType) ? BlogType.Media : BlogType.Text,
            contentIds: [blogId],
            status: 'confirmed',
          },
          blogTitle || 'Blog Generation Cost',
        );

        // Queue the blog for review using Claude 3.7 to make it more human-like
        try {
          const isReviewRequired = this.configService.get('featureFlags.isReviewRequired');

          if (isReviewRequired) {
            // Queue blog review to make content more human-like
            const reviewQueued = await this.blogGenerationService.queueBlogForReview(
              blogId,
              blogContent,
              {
                title: blogTitle || title,
                blogTone,
                blogLanguage,
                perspective: pov,
                seoKeywords: blogOutline.keywords,
                blogSize,
                wordCountApprox,
                sourceType: job.data.sourceType,
                sourceName: job.data.sourceName,
                transcription: job.data.transcription,
                prompt: job.data.prompt,
              },
            );

            if (reviewQueued) {
              this.logger.log(`Queued blog ${blogId} for human-like review`);
            } else {
              this.logger.warn(`Failed to queue blog ${blogId} for human-like review`);
            }
          } else {
            // Skip review queue and send event to keyword generate queue
            const sourceContent = inputText || '';
            const keywordsQueueData = {
              ...job.data,
              blogContent: blogContent,
              blogTitle: blogTitle || title,
              sourceContent: sourceContent || '',
            };
            await this.generateBlogKeywordsQueue.add(keywordsQueueData, {
              ...JOB_OPTIONS,
              jobId: blogId,
            });
            this.logger.log(
              `Review queue bypassed for blog ${blogId}, sent directly to keyword generation`,
            );
          }
        } catch (reviewError) {
          // Non-critical error
          this.logger.error(`Error queueing blog ${blogId} for review: ${reviewError.message}`);
        }
      } catch (error) {
        this.logger.error(`Failed to update blog service ${blogId}`, error.message);
        await this.handleContentGenerationFailure(
          job,
          bid,
          blogId,
          'Failed to update blog service: ' + error.message,
        );
        throw error;
      }
    });
  }

  @OnQueueCompleted()
  async onCompleted({ data: { bid, generationMode, creditTransactionId } }: Job<BlogQueuePayload>) {
    if (generationMode === 'auto' && creditTransactionId) {
      await this.creditTransactionService.confirmTransaction(bid, creditTransactionId);
    }
  }

  @OnQueueFailed()
  async onFailed(
    { data: { bid, blogId, creditTransactionId } }: Job<BlogQueuePayload>,
    error: Error,
  ) {
    if (creditTransactionId) {
      await this.creditTransactionService.refund(bid, creditTransactionId);
      this.logger.error(
        { err: error, bid, blogId, creditTransactionId },
        'Failed to generate content, credit refunded.',
      );
    }
  }

  async generateBlogSections({
    job,
    blogId,
    bid,
    blogOutline,
    blogLanguage,
    eachSection,
    blogTone,
    pov,
    contentImagesMapping,
    blogSize,
    inputText = '',
    transcript,
    customInstruction,
    singleShotBlog,
    sourceSimilarity,
    includeQuotation,
  }: {
    job: Job<BlogQueuePayload>;
    blogId: string;
    bid: string;
    blogOutline: Blog['blogOutline'];
    blogLanguage: Blog['blogLanguage'];
    eachSection: number;
    blogTone: BlogTone;
    pov: Blog['pov'];
    contentImagesMapping: Record<string, string>;
    blogSize: BlogSize;
    inputText?: string;
    transcript?: YouTubeTranscript[];
    customInstruction?: string;
    singleShotBlog: boolean;
    sourceSimilarity?: number;
    includeQuotation?: boolean;
  }): Promise<string> {
    // Declare SEO optimization data at method level
    let seoOptimizationData: object[] = [];
    let structuredSeoKeyFindings: string[] = [];
    let structuredSeoCitations: string[] = [];

    // Get structured SEO data from queue first, then fallback to database
    try {
      // Check queue data first for structured SEO information
      if (job.data.structuredSeoData) {
        this.logger.debug(`Using structured SEO data from queue for blog ${blogId}`);
        seoOptimizationData = this.transformStructuredSeoDataToOptimizationFormat(
          job.data.structuredSeoData,
        );
        structuredSeoKeyFindings = job.data.structuredSeoData.keyFindings || [];
        structuredSeoCitations = job.data.structuredSeoData.citations || [];
      } else {
        // Fallback to database lookup
        const blog = await this.blogService.findById(bid, blogId);
        if (blog?.webSearchSeoKeywords) {
          this.logger.debug(`Using stored SEO data for blog ${blogId}`);
          seoOptimizationData = this.transformStructuredSeoDataToOptimizationFormat(
            blog.webSearchSeoKeywords,
          );
          structuredSeoKeyFindings = blog.webSearchSeoKeywords.keyFindings || [];
          structuredSeoCitations = blog.webSearchSeoKeywords.citations || [];
        }
      }
    } catch (error) {
      this.logger.warn(`Failed to fetch SEO data for blog ${blogId}: ${error.message}`);
      // Continue without SEO data
      seoOptimizationData = [];
      structuredSeoKeyFindings = [];
      structuredSeoCitations = [];
    }

    // For shorter blogs, use orchestrated multi-agent generation
    if (singleShotBlog) {
      try {
        // Using the new orchestrated blog generation for single-shot blogs
        this.logger.log('Using orchestrated blog generation for single-shot blog');

        this.gatewayService.sendBlogStatusUpdate(bid, {
          _id: bid,
          status: 'content_generating',
          percentComplete: 10,
        });

        // Create sourceContent from inputText to avoid null reference
        const sourceContent = inputText || '';

        // Use the new blog generation service
        const result = await this.blogGenerationService.generateCompleteBlog({
          blogId,
          topic: inputText || '',
          language: blogLanguage,
          blogTone,
          blogSize,
          pov: pov || 'neutral',
          customInstruction,
          tldrPosition: BlogTldrPosition.none,
          seoInputKeywords: blogOutline.keywords || [],
          sourceContent, // Pass the sourceContent explicitly
        });

        // Save generation cost to the blog model as a simple number
        if (result.generationCost) {
          await this.blogService.update(bid, blogId, {
            generationCost: result.generationCost.totalCost,
            generationCostBreakdown: result.generationCost.breakdown,
          });

          this.logger.log(
            `Generation cost saved for blog ${blogId}: $${result.generationCost.totalCost.toFixed(4)}`,
          );
        }

        // We also have access to the research data and outline which could be saved for future use
        this.logger.log('Orchestrated blog generation completed successfully');

        const keywordsQueueData = {
          ...job.data,
          blogContent: result.content,
          blogTitle: result.title,
          sourceContent: sourceContent || '',
        };

        await this.generateBlogKeywordsQueue.add(keywordsQueueData, {
          ...JOB_OPTIONS,
          jobId: blogId,
        });
        this.logger.log(
          `Review queue bypassed for orchestrated blog ${blogId}, sent directly to keyword generation`,
        );

        this.gatewayService.sendBlogStatusUpdate(bid, {
          _id: bid,
          status: 'content_generated',
          percentComplete: 90,
        });

        return result.content;
      } catch (error) {
        console.error(error);
        this.logger.error(
          'Error in orchestrated blog generation, falling back to traditional approach',
          error,
        );
        throw error;
      }
    }

    // Existing section-by-section generation logic
    const tasks = blogOutline.sections.map(async (section, index) => {
      await delay(100);
      const sectionResp = await this.generateSectionContent({
        bid,
        blogSectionOutline: section,
        index,
        totalSections: blogOutline.sections.length,
        blogLanguage,
        eachSection,
        blogTone,
        pov,
        blogSize,
        inputText,
        transcript,
        customInstruction,
        tableOption: section.data?.length > 0 ? 'Include a table' : undefined,
        chartOption: section.data?.length > 0 ? 'Include a chart' : undefined,
        sourceSimilarity,
        blogId,
        title: blogOutline.title,
        includeQuotation,
        seoOptimizationData,
        structuredSeoCitations,
        structuredSeoKeyFindings,
      });
      const title = section.heading || section['title'];

      let blogSectionHtml = sectionResp.content;
      if (sectionResp.svgInstruction) {
        const svgCode = await this.blogGenerationService.generateSvg({
          svgInstruction: sectionResp.svgInstruction,
        });

        const pTagCount = (sectionResp.content.match(/<p>/g) || []).length;

        if (pTagCount >= 3) {
          const parts = sectionResp.content.split(/<p>/);
          parts.splice(3, 0, `${svgCode}`);
        }

        // only keep the svg code <svg>...</svg>
        const parsedSvgCode = svgCode.match(/<svg[^>]*>(.*?)<\/svg>/)?.[1];

        if (parsedSvgCode) {
          blogSectionHtml = `${sectionResp.content}${parsedSvgCode}`;
        }

        blogSectionHtml += `<!--SECTION_END_${index}-->`;
      }

      const processedHtml =
        await this.blogGenerationService.processBlogSectionHtml(blogSectionHtml);

      if (contentImagesMapping[title]) {
        return `<img src="${contentImagesMapping[title]}" style="width: 100%; height: auto; display: block;" alt="${title}" /><br>${processedHtml}`;
      }

      return processedHtml;
    });

    const sectionsContent = await Promise.all(tasks);
    return sectionsContent.join('<br>');
  }

  async generateSectionContent({
    bid,
    blogSectionOutline,
    index,
    totalSections,
    blogLanguage,
    eachSection,
    blogTone,
    pov,
    blogSize,
    inputText,
    transcript,
    tableOption,
    chartOption,
    sourceSimilarity,
    blogId,
    title,
    includeQuotation = true,
    seoOptimizationData,
    structuredSeoCitations,
    structuredSeoKeyFindings,
  }: {
    bid: string;
    blogSectionOutline: Blog['blogOutline']['sections'][0];
    index: number;
    totalSections: number;
    blogLanguage: Blog['blogLanguage'];
    eachSection: number;
    blogTone: BlogTone;
    pov: Blog['pov'];
    blogSize: BlogSize;
    inputText: string;
    transcript?: YouTubeTranscript[];
    customInstruction?: string;
    tableOption?: string;
    chartOption?: string;
    sourceSimilarity?: number;
    blogId: string;
    title: string;
    includeQuotation?: boolean;
    seoOptimizationData?: object[];
    structuredSeoCitations?: string[];
    structuredSeoKeyFindings?: string[];
  }): Promise<{ content: string; svgInstruction: string }> {
    const isLastSection = index >= totalSections - 1;

    // parse transcript texts from caption indexes
    const transcriptText = blogSectionOutline.captionIndexes
      ?.map((c) => {
        return transcript
          ?.slice(c.startCaptionIndex, c.endCaptionIndex)
          .filter((t) => t && typeof t === 'object' && 'text' in t)
          .map((t) => `${t.startTime?.toFixed(2)}-${t.endTime?.toFixed(2)}: ${t.text}`)
          .join('\n');
      })
      .join('\n');

    const blogGenInstruction = this.constructBlogGenInstruction({
      section: blogSectionOutline,
      isLastSection,
      blogLanguage,
      eachSection,
      blogTone,
      pov,
      blogSize,
      inputText:
        transcriptText && transcriptText.length > 50 ? transcriptText : inputText || inputText,
      hasTimestampedTranscript: !!transcriptText,
      tableOption,
      chartOption,
      title,
      includeQuotation,
    });

    const { subscriptionPlan } = await this.businessService.findOne(bid);
    const model = getPlanModel(subscriptionPlan, TASK.BLOG);

    const sectionResp = await this.blogGenerationService.generateBlogSection({
      model,
      blogSectionOutline,
      prompt: blogGenInstruction,
      sourceSimilarity,
      blogTone,
      includeQuotation,
      seoOptimizationData,
      structuredSeoCitations,
      structuredSeoKeyFindings,
    });

    // Track the cost for this section
    if (sectionResp.metadata?.estimatedCost > 0) {
      try {
        // Get current cost breakdown if exists
        const blog = await this.blogService.findById(bid, blogId);
        const costBreakdown = blog.generationCostBreakdown || [];

        // Add this section's cost
        costBreakdown.push({
          step: `section_${index + 1}`,
          cost: sectionResp.metadata.estimatedCost,
          model,
          timestamp: new Date(),
        });

        // Calculate total cost
        const totalCost = costBreakdown.reduce((sum, item) => sum + item.cost, 0);

        // Update blog with new costs
        await this.blogService.update(bid, blogId, {
          generationCost: totalCost,
          generationCostBreakdown: costBreakdown,
        });

        this.logger.debug(
          `Cost for section ${index + 1}/${totalSections} (${blogSectionOutline.heading}): $${sectionResp.metadata.estimatedCost.toFixed(6)}, total: $${totalCost.toFixed(6)}`,
        );
      } catch (error) {
        this.logger.error('Error tracking section cost', error);
      }
    }

    return sectionResp;
  }

  generateBlogContentImagesMapping(
    blogOutline: Blog['blogOutline'],
    contentImages: string[],
  ): Record<string, string> {
    const totalSections = blogOutline?.sections?.length || 0;
    const totalImages = contentImages?.length || 0;
    const interval = Math.floor(totalSections / totalImages);
    let currentImageIndex = 0;
    const mapping: Record<string, string> = {};

    if (totalSections === 0 || totalImages === 0) {
      return {}; // Return an empty mapping if no sections or images
    }

    // If only one image, place it in the middle section
    if (totalImages === 1) {
      const middleIndex = Math.floor(totalSections / 2);
      for (let i = 1; i < totalSections; i++) {
        const title = blogOutline.sections[i].heading || blogOutline.sections[i]['title'];
        // Start from 1 to skip the first section
        if (i === middleIndex) {
          mapping[title] = contentImages[0];
        } else {
          mapping[title] = null;
        }
      }
      return mapping;
    }

    for (let i = 1; i < totalSections; i++) {
      // Start from 1 to skip the first section
      const title = blogOutline.sections[i].heading || blogOutline.sections[i]['title'];
      if ((i + 1) % interval === 0 && currentImageIndex < totalImages) {
        mapping[title] = contentImages[currentImageIndex];
        currentImageIndex++;
      } else {
        mapping[title] = null; // No image for this section
      }
    }

    // If there are still unused images, map them to the remaining sections
    for (let i = 1; i < totalSections && currentImageIndex < totalImages; i++) {
      // Start from 1 to skip the first section
      const title = blogOutline.sections[i].heading || blogOutline.sections[i]['title'];
      if (!mapping[title]) {
        mapping[title] = contentImages[currentImageIndex];
        currentImageIndex++;
      }
    }

    return mapping;
  }

  constructBlogGenInstruction({
    section,
    isLastSection,
    blogLanguage,
    eachSection,
    blogTone,
    pov,
    blogSize,
    inputText,
    hasTimestampedTranscript,
    title,
    includeQuotation,
  }: {
    section: Blog['blogOutline']['sections'][0];
    isLastSection: boolean;
    blogLanguage: string;
    eachSection: number;
    blogTone: string;
    pov: string | undefined;
    blogSize: 'mini' | 'small' | 'medium' | 'large' | 'x-large';
    inputText: string | undefined;
    hasTimestampedTranscript: boolean;
    tableOption?: string;
    chartOption?: string;
    title: string;
    includeQuotation: boolean;
  }): string {
    const sectionDetails = this.formatSectionDetails(section, includeQuotation);
    const sizeInstruction = this.getSizeInstruction(blogSize, isLastSection);
    const styleInstruction = `Write in ${blogTone} tone from ${pov || 'neutral'} perspective in ${blogLanguage}.`;
    const formattingInstruction = this.getFormattingInstruction();

    // SEO instructions for section-specific keywords
    const sectionKeywords = section.targetedKeywords || [];
    const sectionFindings = section.keyFindings || [];

    const keywordInstructions = sectionKeywords.length
      ? `Target keywords: ${sectionKeywords.join(', ')} - include naturally in headings and content.`
      : '';

    const findingsInstructions = sectionFindings.length
      ? `Research insights: ${sectionFindings.join('; ')} - incorporate when relevant to enhance content.`
      : '';

    const additionalContentInstructions = [];
    if (section.bulletPoints?.length > 0) {
      additionalContentInstructions.push(`Key points: ${section.bulletPoints.join(', ')}`);
    }

    if (includeQuotation && section.quotes?.length > 0) {
      additionalContentInstructions.push(`Available quotes: ${section.quotes.join('; ')}`);
    }

    if (section.svgPrompt && section.shouldGenerateChart) {
      const shuffledColors = [...predefinedColors].sort(() => Math.random() - 0.5).slice(0, 5);
      additionalContentInstructions.push(
        `Generate professional SVG chart based on: ${section.svgPrompt}. Use colors: ${shuffledColors.join(', ')}`,
      );
    }

    if (section.data && !Array.isArray(section.data)) {
      section.data = Object.keys(section.data).reduce((prev, curr) => {
        if (section.data[curr] === '') {
          prev.push(curr);
        }
        return prev;
      }, []);
    }

    if (section.data?.length > 0 && section.shouldGenerateTable) {
      additionalContentInstructions.push(`Include table with data: ${section.data.join(', ')}`);
    }

    return `
Create ${eachSection}-word section for "${title}" - Section: ${section.heading}

Content Guidelines:
- Source material: ${inputText ? `"${inputText}"` : 'Use provided context'}
- ${styleInstruction}
- ${hasTimestampedTranscript ? 'Reference relevant transcript timestamps.' : ''}
- Use simple, clear language (avoid complex vocabulary)
- ${keywordInstructions}
- ${findingsInstructions}

Structure:
${sectionDetails}
${additionalContentInstructions.length ? `Elements: ${additionalContentInstructions.join(' | ')}` : ''}

Requirements:
${sizeInstruction}
${formattingInstruction}

Focus: Source-first content with natural SEO integration. Maximum ${eachSection} words.
`;
  }

  private formatSectionDetails(
    section: Blog['blogOutline']['sections'][0],
    includeQuotation = true,
  ): string {
    if (typeof section.quotes === 'string') {
      section.quotes = [section.quotes];
    }

    const details = [
      `Heading: ${section.heading}`,
      section.bulletPoints?.length > 0 ? `Talking Points: ${section.bulletPoints.join('. ')}` : ``,
      section.notes && `Notes: ${section.notes}`,
      section.keywords?.length > 0 && `Keywords: ${section.keywords.join(', ')}`,
      section.metaDescription && `Meta Description: ${section.metaDescription}`,
      section.data?.length > 0 && `Data: ${section.data.join(', ')}`,
      includeQuotation && section.quotes?.length > 0 && `Quotes: ${section.quotes.join('; ')}`,
      section.relevantContent && `Relevant Content: ${section.relevantContent}`,
    ].filter(Boolean);

    return details.join('\n');
  }

  private getSizeInstruction(
    blogSize: 'mini' | 'small' | 'medium' | 'large' | 'x-large',
    isLastSection: boolean,
  ): string {
    const sizeDesc =
      blogSize === 'small' ? 'concise' : blogSize === 'medium' ? 'moderate' : 'comprehensive';
    const lastSectionNote = isLastSection
      ? 'For this final section write the conclusion of the blog without bullet points.'
      : 'Do not conclude or write any conclusion for the post or preview upcoming sections. This is just a section of the blog.';

    return `Generate ${sizeDesc} content. ${lastSectionNote}`;
  }

  private getFormattingInstruction(): string {
    return `
Use these HTML tags:
- <h2> for main heading
- <h3> to <h6> for subheadings
- <p> for paragraphs
- <ul>, <ol>, <li> for lists
- <b>, <i>, <em> for emphasis
- <blockquote> for quotes
- <code> for code snippets
- <br> for line breaks
- <table>, <tr>, <th>, <td> for tables

No overall wrapping tag needed.
`;
  }

  private convertMarkdownBoldToHtml(content: string): string {
    return content.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
  }

  async handleContentGenerationFailure(
    job: Job<BlogQueuePayload>,
    bid: string,
    blogId: string,
    failReason: string,
  ) {
    this.gatewayService.sendBlogStatusUpdate(job.data.uid, {
      _id: blogId,
      status: 'content_generation_failed',
      identifier: job.data.identifier,
      failReason,
      failedQueue: JOB_QUEUES.GENERATE_BLOG_CONTENT,
    });

    if (job.attemptsMade >= job.opts.attempts) {
      await this.blogService.update(bid, blogId, {
        status: 'content_generation_failed',
        failReason,
        failedQueue: JOB_QUEUES.GENERATE_BLOG_CONTENT,
      });
    }
  }

  private transformStructuredSeoDataToOptimizationFormat(structuredSeoData: any): object[] {
    if (!structuredSeoData) return [];

    try {
      // Convert structured SEO data to optimization format
      const optimizationData = [];

      // Extract keywords from different sections if available
      const sections = ['h1', 'h2', 'h3', 'content', 'title', 'meta'];

      sections.forEach((section) => {
        if (structuredSeoData[section]?.length) {
          structuredSeoData[section].forEach((keywordItem) => {
            if (keywordItem && keywordItem.keyword) {
              optimizationData.push({
                keyword: keywordItem.keyword,
                section,
                min: keywordItem.min || 1,
                max: keywordItem.max || 3,
                density: 0, // Will be calculated during content analysis
                recommendations: this.generateKeywordRecommendations(
                  keywordItem.keyword,
                  section,
                  keywordItem,
                ),
              });
            }
          });
        }
      });

      return optimizationData;
    } catch (error) {
      this.logger.warn('Error transforming structured SEO data:', error);
      return [];
    }
  }

  private generateKeywordRecommendations(keyword: string, section: string, data: any): string[] {
    const recommendations = [];

    if (data.current < data.expected.min) {
      recommendations.push(
        `Increase usage of "${keyword}" in ${section} (current: ${data.current}, recommended: ${data.expected.min}-${data.expected.max})`,
      );
    } else if (data.current > data.expected.max) {
      recommendations.push(
        `Reduce usage of "${keyword}" in ${section} to avoid keyword stuffing (current: ${data.current}, recommended: ${data.expected.min}-${data.expected.max})`,
      );
    } else {
      recommendations.push(`Good keyword density for "${keyword}" in ${section}`);
    }

    return recommendations;
  }
}
