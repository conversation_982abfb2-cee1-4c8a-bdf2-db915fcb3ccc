import type { BlogQueuePayload } from '@blog/blog.model';
import type { Job, Queue } from 'bull';

import { InjectQueue, OnQueueCompleted, OnQueueFailed, Process, Processor } from '@nestjs/bull';
import { NotFoundException } from '@nestjs/common';

import { CreditTransactionService } from '@/resources/credit-transaction/credit-transaction.service';
import { JOB_OPTIONS, JOB_QUEUES } from '@/common/constants';
import { BlogGenerationService } from '@/blog-generation/blog-generation.service';
import { getPlanModel, TASK } from '@/llm/llm.models';
import { BusinessService } from '@/business/business.service';
import { GatewayService } from '@/modules/gateway/gateway.service';
import { BaseProcessor } from '@/common/queue/base.processor';
import { OpenaiService } from '@/openai/openai.service';
import { Blog } from '@/blog/blog.model';

import { BlogGenerationMode } from './blog.enums';
import { BlogService } from './blog.service';

interface SectionScore {
  index: number;
  chartScore: number;
  tableScore: number;
}

@Processor(JOB_QUEUES.GENERATE_BLOG_OUTLINE)
export class BlogGenerateOutlineProcessor extends BaseProcessor {
  protected readonly queueName = JOB_QUEUES.GENERATE_BLOG_OUTLINE;

  constructor(
    @InjectQueue(JOB_QUEUES.GENERATE_BLOG_OUTLINE) blogOutlineQueue: Queue,
    @InjectQueue(JOB_QUEUES.GENERATE_BLOG_CONTENT) private generateBlogContentQueue: Queue,
    @InjectQueue(JOB_QUEUES.SEO_SEARCH_RELEVANT_CONTENT)
    private seoSearchRelevantContentQueue: Queue,
    private readonly creditTransactionService: CreditTransactionService,
    private readonly blogGenerationService: BlogGenerationService,
    private readonly businessService: BusinessService,
    private readonly gatewayService: GatewayService,
    private readonly openaiService: OpenaiService,
    private readonly blogService: BlogService,
  ) {
    super(blogOutlineQueue);
  }

  @Process({ concurrency: 10 })
  protected async process(job: Job<BlogQueuePayload>): Promise<void> {
    return this.processWithLock(job, async (job) => {
      const {
        blogId,
        bid,
        uid,
        email,
        inputLanguage = 'English',
        blogLanguage = 'English',
        country,
        generationMode,
        identifier,
        seoInputKeywords,
      } = job.data;

      this.logger.log({ attempts: job.attemptsMade, blogId });

      const { transcriptionSummary, transcription, prompt } = job.data;
      let inputPrompt = transcriptionSummary || transcription || prompt || '';

      if (!inputPrompt) {
        const blog = await this.blogService.findBlogById(blogId);
        if (!blog) {
          throw new NotFoundException(`Blog with the id ${blogId} not found.`);
        }
        job.data.transcriptionSummary = blog.transcriptionSummary;
        job.data.transcription = blog.transcription;
        job.data.prompt = blog.prompt;
        inputPrompt = blog.transcription || blog.transcriptionSummary || blog.prompt;
      }

      if (!inputPrompt) {
        throw new Error('No context found for blog outline generation.');
      }

      const totalTokens = this.openaiService.countTokens(inputPrompt);

      this.logger.log(
        `Generating blog outline for blog ${blogId} for ${email} total tokens in input: ${totalTokens}`,
      );

      try {
        await this.blogService.update(bid, blogId, { status: 'outline_generating' });
        this.gatewayService.sendBlogStatusUpdate(uid, {
          _id: blogId,
          identifier,
          status: 'outline_generating',
        });

        const jobData = job.data;

        jobData.keywords ??= seoInputKeywords ?? [];

        const { blogOutline, ...r } = await this.generateOutlineWithBlogGenerationService(jobData);
        const blogTitle = r.blogTitle;
        const keywords = r.keywords;

        await this.updateBlogWithOutline(
          bid,
          blogId,
          blogTitle,
          blogOutline,
          keywords,
          inputLanguage,
          blogLanguage,
          uid,
          identifier,
          generationMode,
          email,
        );

        if (generationMode === 'assisted') {
          job.moveToCompleted();
          job.progress(100);
          return true;
        }

        // Always send directly to content generation, skipping keyword analysis
        await this.generateBlogContentQueue.add(
          { ...jobData, blogTitle, blogOutline, keywords, country, blogId, uid },
          {
            ...JOB_OPTIONS,
            jobId: blogId,
            attempts: 3,
            backoff: {
              type: 'exponential',
              delay: 2000,
            },
          },
        );
        this.logger.debug(
          `Blog ${blogId} forwarded to content generation, skipping keyword analysis`,
        );

        await job.progress(100);
      } catch (error) {
        this.logger.error(`Failed to generate outline for blog ${blogId}:`, error);

        // Check if error is retryable
        if (error['code'] === 'RATE_LIMIT_ERROR' || error['code'] === 'NETWORK_ERROR') {
          await job.retry();
          return;
        }

        throw error;
      }
    });
  }

  @OnQueueCompleted()
  protected async handleCompleted(job: Job<BlogQueuePayload>) {
    const { bid, generationMode, creditTransactionId } = job.data;
    if (generationMode === 'assisted' && creditTransactionId) {
      await this.creditTransactionService.confirmTransaction(bid, creditTransactionId);
    }
  }

  @OnQueueFailed()
  protected async handleFailedJob(job: Job<BlogQueuePayload>, error: Error): Promise<void> {
    const { bid, creditTransactionId } = job.data;
    if (creditTransactionId) {
      await this.creditTransactionService.refund(bid, creditTransactionId);
      this.logger.error(`Couldn't generate outline, credit refunded.`, error);
    }
  }

  private async generateOutlineWithBlogGenerationService(data: BlogQueuePayload) {
    const {
      bid,
      blogId,
      blogLanguage,
      pov,
      blogTone,
      blogSize,
      transcription,
      transcript,
      transcriptionSummary,
      prompt,
      customInstruction,
      sourceName,
      sourceType,
      tableOption,
      chartOption,
      tldrPosition,
      seoInputKeywords,
      seoOptimizationData,
    } = data;

    // Fetch web search SEO keywords if they exist
    let enhancedSeoOptimizationData = seoOptimizationData;
    let structuredSeoData = null;

    try {
      const blog = await this.blogService.findById(bid, blogId);

      // Check for structured SEO data from queue first, then fallback to database
      if (data.structuredSeoData) {
        structuredSeoData = data.structuredSeoData;
      } else if (blog?.webSearchSeoKeywords) {
        structuredSeoData = blog.webSearchSeoKeywords;
      }

      if (structuredSeoData) {
        this.logger.debug(`Using structured SEO data for blog ${blogId}`);

        // Transform structured keywords for enhanced SEO optimization
        const structuredSeoOptimizationData = [];

        // Extract keywords from different sections for section targeting
        ['h1', 'h2', 'h3', 'content', 'title', 'meta'].forEach((tag) => {
          if (structuredSeoData[tag]?.length) {
            structuredSeoData[tag].forEach((keywordItem) => {
              structuredSeoOptimizationData.push({
                tag,
                keyword: keywordItem.keyword,
                min: keywordItem.min,
                max: keywordItem.max,
                priority: tag === 'h2' || tag === 'h3' ? 'high' : 'medium', // Prioritize heading keywords
              });
            });
          }
        });

        // Add key findings and citations as context
        if (structuredSeoData.keyFindings?.length) {
          structuredSeoOptimizationData.push({
            tag: 'insights',
            content: structuredSeoData.keyFindings,
            type: 'findings',
          });
        }

        if (structuredSeoData.citations?.length) {
          structuredSeoOptimizationData.push({
            tag: 'sources',
            content: structuredSeoData.citations,
            type: 'citations',
          });
        }

        // Merge with existing seoOptimizationData
        enhancedSeoOptimizationData = [
          ...(seoOptimizationData || []),
          ...structuredSeoOptimizationData,
        ];

        this.logger.debug(
          `Enhanced SEO optimization data with ${structuredSeoOptimizationData.length} structured items for blog ${blogId}`,
        );
      }
    } catch (error) {
      this.logger.warn(`Failed to fetch structured SEO data for blog ${blogId}: ${error.message}`);
      // Continue with original seoOptimizationData
    }

    const { subscriptionPlan } = await this.businessService.findOne(bid);
    const model = getPlanModel(subscriptionPlan, TASK.OUTLINE);

    const context = transcription || transcriptionSummary || prompt;
    const response = await this.blogGenerationService.generateOutline({
      model,
      context,
      sourceName,
      sourceType,
      blogTone,
      blogSize,
      language: blogLanguage,
      pov,
      transcript,
      customInstruction,
      tableOption,
      chartOption,
      tldrPosition,
      seoInputKeywords,
      seoOptimizationData: enhancedSeoOptimizationData,
    });

    const blogOutlineResponse = response.outline;

    if (!blogOutlineResponse?.title || !blogOutlineResponse?.sections) {
      throw new Error('Error generating blog outline using blogGenerationService, invalid format.');
    }

    const outline = this.validateAndCorrectOutline(blogOutlineResponse, chartOption, tableOption);

    // Track cost if available
    if (response.metadata?.estimatedCost > 0) {
      try {
        // Get current cost breakdown if exists
        const blog = await this.blogService.findById(bid, blogId);
        const costBreakdown = blog.generationCostBreakdown || [];

        // Add this step's cost
        costBreakdown.push({
          step: 'outline_generation',
          cost: response.metadata.estimatedCost,
          model,
          timestamp: new Date(),
        });

        // Calculate total cost
        const totalCost = costBreakdown.reduce((sum, item) => sum + item.cost, 0);

        // Update blog with new costs
        await this.blogService.update(bid, blogId, {
          generationCost: totalCost,
          generationCostBreakdown: costBreakdown,
        });

        this.logger.debug(
          `Cost for outline generation: $${response.metadata.estimatedCost.toFixed(6)}, total: $${totalCost.toFixed(6)}`,
        );
      } catch (error) {
        this.logger.error('Error tracking outline generation cost', error);
      }
    }

    return {
      blogOutline: outline,
      blogTitle: outline.title,
      keywords: outline.keywords || [],
    };
  }

  private async updateBlogWithOutline(
    bid: string,
    blogId: string,
    blogTitle: string,
    blogOutline: Blog['blogOutline'],
    keywords: string[],
    inputLanguage: string,
    blogLanguage: string,
    uid: string,
    identifier: string,
    generationMode: BlogGenerationMode,
    email: string,
  ) {
    const generationStatus = `Generated blog outline and title next step is to search for SEO`;
    await this.blogService.update(bid, blogId, {
      title: blogTitle,
      blogOutline,
      keywords,
      status: 'outline_generated',
      inputLanguage,
      blogLanguage,
      generationStatus,
    } as Blog);
    this.logger.log(`${generationStatus} for blog ${blogId} email ${email}`);
    this.gatewayService.sendBlogStatusUpdate(uid, {
      _id: blogId,
      identifier,
      generationMode,
      status: 'outline_generated',
    });
  }

  private validateAndCorrectOutline(
    blogOutlineResponse: Blog['blogOutline'],
    chartOption = 'No Chart',
    tableOption = 'No Table',
  ): Blog['blogOutline'] {
    if (!blogOutlineResponse?.sections || !Array.isArray(blogOutlineResponse.sections)) {
      return blogOutlineResponse;
    }

    try {
      const sections = [...blogOutlineResponse.sections];

      // Calculate section scores based on content length and data presence
      const sectionScores: SectionScore[] = sections.map((section, index) => ({
        index,
        chartScore: section.data?.length ? section.data.length * 20 : 0,
        tableScore: section.data?.length ? section.data.length * 20 : 0,
      }));

      // Reset all chart and table flags
      sections.forEach((section) => {
        section.shouldGenerateChart = false;
        section.shouldGenerateTable = false;
      });

      // Handle Chart Options
      switch (chartOption) {
        case 'A single chart':
        case 'One Chart': {
          // Find the section with the highest chart score
          const bestChartSection = sectionScores.reduce((best, current) =>
            current.chartScore > best.chartScore ? current : best,
          );
          if (bestChartSection) {
            sections[bestChartSection.index].shouldGenerateChart = true;
          }
          break;
        }
        case 'Two Charts': {
          // Sort sections by chart score and take top 2
          const topChartSections = [...sectionScores]
            .sort((a, b) => b.chartScore - a.chartScore)
            .slice(0, 2);

          topChartSections.forEach((section) => {
            sections[section.index].shouldGenerateChart = true;
          });
          break;
        }
        case 'Charts for Each Section': {
          sections.forEach((section) => {
            section.shouldGenerateChart = true;
          });
          break;
        }
        case 'No Chart':
        default: {
          // Charts already reset to false above
          break;
        }
      }

      // Handle Table Options
      switch (tableOption) {
        case 'A single table':
        case 'One Table': {
          // First try to find a section without a chart
          const availableSections = sectionScores
            .filter((section) => !sections[section.index].shouldGenerateChart)
            .sort((a, b) => b.tableScore - a.tableScore);

          if (availableSections.length > 0) {
            sections[availableSections[0].index].shouldGenerateTable = true;
          } else {
            // If all sections have charts, pick the one with highest table score
            const bestSection = sectionScores.reduce((best, current) =>
              current.tableScore > best.tableScore ? current : best,
            );
            sections[bestSection.index].shouldGenerateTable = true;
          }
          break;
        }
        case 'Two Table': {
          // Try to find sections without charts first
          const availableSections = sectionScores
            .filter((section) => !sections[section.index].shouldGenerateChart)
            .sort((a, b) => b.tableScore - a.tableScore);

          let sectionsToUse;
          if (availableSections.length >= 2) {
            // We have enough sections without charts
            sectionsToUse = availableSections.slice(0, 2);
          } else {
            // We need to use some sections that have charts
            const sectionsWithCharts = sectionScores
              .filter((section) => sections[section.index].shouldGenerateChart)
              .sort((a, b) => b.tableScore - a.tableScore);

            sectionsToUse = [
              ...availableSections,
              ...sectionsWithCharts.slice(0, 2 - availableSections.length),
            ];
          }

          sectionsToUse.forEach((section) => {
            sections[section.index].shouldGenerateTable = true;
          });
          break;
        }
        case 'Table for Each Section': {
          sections.forEach((section) => {
            section.shouldGenerateTable = true;
          });
          break;
        }
        case 'No Table':
        default: {
          // All table flags already set to false
          break;
        }
      }

      return {
        ...blogOutlineResponse,
        sections,
      };
    } catch (error) {
      this.logger.error('Error in validateAndCorrectOutline:', error);
      return blogOutlineResponse;
    }
  }
}
