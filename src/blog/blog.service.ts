import type { ShopifyProductDto } from './dto/shopify-product.dto';
import type { PaginatedBlogsDto } from './dto/blog.dto';
import type { BlogFilterDto } from './dto/blog-filter.dto';
import type { CreateBlogDto } from './dto/create-blog.dto';
import type { UpdateBlogDto } from './dto/update-blog.dto';
import type { AxiosInstance } from 'axios';
import type { User } from '@/user/user.model';

import type { Model } from 'mongoose';

import {
  InternalServerErrorException,
  UnprocessableEntityException,
  NotFoundException,
  Injectable,
  Logger,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { InjectQueue } from '@nestjs/bull';
import { ObjectId } from 'mongodb';
import { Queue } from 'bull';
import { load } from 'cheerio';
import mongoose from 'mongoose';
import pRetry from 'p-retry';
import geoip from 'geoip-lite';
import axios from 'axios';

import { AffiliateLinkTrackingService } from '@/monetization/affiliate-link-tracking/affiliate-link-tracking.service';
// import { CreditTransactionService } from '@resources/credit-transaction/credit-transaction.service';
import { JOB_OPTIONS, JOB_QUEUES } from '@/common/constants';
import { PublishingService } from '@/publishing/publishing.service';
import { AnalyticsService } from '@/analytics/analytics.service';
import { OpenaiService } from '@/openai/openai.service';
import { UserService } from '@/user/user.service';
import config from '@/common/configs/config';

import { BlogPublishStatus, BlogSourceName, BlogSourceType } from './blog.enums';

import { getPublishStatus } from './blog.utils';
import { PublishBlogDto } from './dto/publish-blog.dto';
import { BLOG_STATUS } from './blog.interface';
import { Blog } from './blog.model'; // BlogType,

type UserIds = { bid: string; uid: string };

@Injectable()
export class BlogService {
  private blogifyML: AxiosInstance;
  private tenMinutesInMilliseconds = 10 * 60 * 1000;

  private readonly logger = new Logger(this.constructor.name);
  constructor(
    @InjectQueue(JOB_QUEUES.GENERATE_AFFILIATE_KEYWORDS)
    private generateAffiliateKeywordsQueue: Queue,
    @InjectQueue(JOB_QUEUES.DISPATCH_TO_PUBLISHERS) private dispatchToPublishersQueue: Queue,
    @InjectQueue(JOB_QUEUES.GENERATE_BLOG_CONTENT) private generateBlogContentQueue: Queue,
    @InjectQueue(JOB_QUEUES.BLOG_REQUEST) private blogRequestQueue: Queue,
    @InjectQueue(JOB_QUEUES.BLOG_REVIEW) private blogReviewQueue: Queue,
    @InjectModel(Blog.name) private readonly blogModel: Model<Blog>,
    private readonly affiliateLinkTrackingService: AffiliateLinkTrackingService,
    // private readonly creditTransactionService: CreditTransactionService,
    private readonly analyticsService: AnalyticsService,
    private readonly openaiService: OpenaiService,
    private readonly userService: UserService,
    private readonly publishingService: PublishingService,
  ) {
    this.blogifyML = axios.create({
      baseURL: config().internalApps.blogifyML.url,
      headers: {
        'x-api-key': config().internalApps.blogifyML.apiKey,
      },
    });
  }

  async generateSocialMediaContent(
    blogId: string,
    prompt: string,
    platform: string,
    link: string,
    emoji: boolean,
    hashtag: boolean,
  ): Promise<string> {
    const { blogLanguage } = await this.blogModel.findById(blogId);
    const content = await this.openaiService.generateSocialMediaContentFromPrompt(
      blogId,
      prompt,
      blogLanguage,
      platform,
      link,
      { include: { emoji, hashtag } },
    );

    return content;
  }

  async generatePromptFromShopifyData(shopifyData: ShopifyProductDto): Promise<any> {
    const run = async () => this.promptAndImagesForShopifyData(shopifyData);

    return pRetry(run, {
      onFailedAttempt: (error) => {
        this.logger.error(
          `Attempt ${error.attemptNumber} failed to scrap shopify data: ${JSON.stringify(
            shopifyData,
          )}. There are ${error.retriesLeft} retries left.`,
        );
      },
      retries: 3,
    });
  }

  async generatePromptFromImageLink(imageLink: string): Promise<string> {
    const run = async () => {
      const response = await this.blogifyML.post('/get-visual-insights', {
        url: imageLink,
      });
      return response.data;
    };

    return pRetry(run, {
      onFailedAttempt: (error) => {
        this.logger.error(
          `Attempt ${error.attemptNumber} failed to generate prompt from image: ${imageLink}. There are ${error.retriesLeft} retries left.`,
        );
      },
      retries: 3,
    });
  }

  async createBlog(
    { bid, uid }: UserIds,
    createBlogDto: CreateBlogDto,
    email: string,
    ip: string,
  ): Promise<any> {
    const createBlogOperation = async () => {
      try {
        this.logger.debug(`Creating blog for ${email} ${bid}-${uid}`);
        const { platforms = [] } = createBlogDto;

        const country = geoip.lookup(ip)?.country as string;

        if (platforms?.length) {
          createBlogDto['publishStatus'] = getPublishStatus({ platforms } as Blog);
        }

        const savedBlog = await this.saveBlog({ ...createBlogDto }, bid, uid, BLOG_STATUS.STARTED);

        // const creditTransaction = await this.creditTransactionService.deductContentCost(
        //   { bid, uid },
        //   {
        //     contentType: ['audio', 'video'].includes(createBlogDto.sourceType)
        //       ? BlogType.Media
        //       : BlogType.Text,
        //     contentIds: [savedBlog._id],
        //   },
        //   createBlogDto.title || 'Blog Generation Cost',
        // );

        // const creditTransactionId = creditTransaction._id;
        const queueResponse = await this.blogRequestQueue.add(
          {
            ...createBlogDto,
            uid,
            bid,
            email,
            blogId: savedBlog._id,
            creditTransactionId: null,
            country,
          },
          { ...JOB_OPTIONS, jobId: savedBlog._id },
        );
        const status = queueResponse.id ? BLOG_STATUS.QUEUED : BLOG_STATUS.FAILED;
        await this.update(bid, savedBlog._id, { status });
        this.logger.debug(`create blog request queued for ${email} ${bid}-${uid}`);

        return {
          _id: savedBlog._id,
          generationMode: savedBlog.generationMode,
          identifier: savedBlog.identifier,
          success: `Blog generation request queued for processing`,
        };
      } catch (error) {
        if (error.message?.includes('WriteConflict')) {
          this.logger.warn(`WriteConflict detected for ${email} ${bid}-${uid}, retrying...`);
          throw error;
        }

        this.logger.error(
          `Failed to queue blog request for ${email} ${bid}-${uid}`,
          error?.message,
        );
        throw new Error(`Blog creation failed: ${error?.message}`);
      }
    };

    const retryOptions = {
      retries: 5,
      minTimeout: 2000,
      maxTimeout: 10000,
      factor: 1.5,
      randomize: true,
      onFailedAttempt: (error) => {
        if (error.message?.includes('WriteConflict')) {
          this.logger.warn(
            `WriteConflict retry attempt ${error.attemptNumber} for ${email} ${bid}-${uid}. ${error.retriesLeft} retries left.`,
          );
        } else {
          this.logger.error(
            `Failed attempt ${error.attemptNumber} for ${email} ${bid}-${uid}. ${error.retriesLeft} retries left.`,
            error.message,
          );
        }
      },
    };

    try {
      return await pRetry(createBlogOperation, retryOptions);
    } catch (error) {
      const errorMessage = error.message?.includes('WriteConflict')
        ? 'Failed to create blog due to concurrent operation. Please try again.'
        : `Failed to create blog: ${error?.message}`;

      this.logger.error(`Final failure for ${email} ${bid}-${uid}`, error?.message);
      throw new Error(errorMessage);
    }
  }

  async createBlogManual<T>({ bid, uid, user }: UserIds & { user: User }, body: T) {
    try {
      const blog: Partial<Blog> = {
        uid,
        bid,

        prompt: 'Manually created.',
        sourceType: BlogSourceType.prompt,
        sourceName: BlogSourceName.Prompt,

        status: 'completed',
        publishStatus: BlogPublishStatus.published,

        ...body,
      };

      blog.title = blog.title || 'Untitled Blog';
      blog.content = blog.content || 'Start writing your blog here...';
      if (!blog.title) {
        blog.publishStatus = BlogPublishStatus.draft;
      }

      const createdBlog = await new this.blogModel(blog).save();
      const savedBlog = createdBlog['_doc'];

      await this.dispatchToPublishersQueue.add(
        {
          ...savedBlog,
          uid,
          bid,
          email: user.email,
          blogId: savedBlog._id,
          blogTitle: savedBlog.title,
          blogContent: savedBlog.content,
          blogKeywords: savedBlog.keywords,
        },
        { ...JOB_OPTIONS, jobId: savedBlog._id },
      );

      return savedBlog;
    } catch (e) {
      throw new InternalServerErrorException('Manual blog creation failed with error', e);
    }
  }

  async regenerateBlog(id: string, bid: string, uid: string, email: string) {
    try {
      this.logger.debug({ email, bid, uid, blogId: id }, 'Regenerating blog');

      const blog = await this.blogModel.findOne({ bid, _id: id }).exec();
      if (!blog) {
        throw new NotFoundException(`Blog with id ${id} not found`);
      }

      const job = await this.blogRequestQueue.getJob(blog._id);

      let status = BLOG_STATUS.FAILED;
      let message = '';
      if (job?.attemptsMade < 10) {
        await job.retry();
        status = BLOG_STATUS.QUEUED;
        message = `Blog ${id} re-generation request queued for processing`;
        this.logger.debug({ email, bid, uid, blogId: id }, 'Regenerate blog request queued');
      } else {
        message = `Too many attempts made. Couldn't regenerate the blog.`;
        this.logger.debug(
          { email, bid, uid, blogId: id, message, attemptsMade: job?.attemptsMade },
          'Regenerate blog request failed',
        );
      }

      await this.update(bid, id, {
        status,
        publishAt: new Date(),
        ...(status === BLOG_STATUS.QUEUED ? { totalRetries: blog.totalRetries + 1 } : {}),
      });

      return {
        status,
        message,
        _id: blog._id,
        identifier: blog.identifier,
        generationMode: blog.generationMode,
      };
    } catch (error) {
      this.logger.error(
        { err: error, email, bid, uid, blogId: id },
        `Failed to queue blog request to re-generate for ${email} ${bid}-${uid}`,
      );
      throw error;
    }
  }

  async saveBlog(
    payload: CreateBlogDto & { thumbnail?: string },
    bid: string,
    uid: string,
    country?: string,
    status?: string,
    content?: string,
  ): Promise<Blog> {
    const retryOptions = {
      retries: 3,
      minTimeout: 1000, // Wait 1 second between retries
      factor: 2, // Exponential backoff factor
      onFailedAttempt: (error) => {
        this.logger.warn(
          `Attempt ${error.attemptNumber} failed to save blog for ${bid}-${uid}. ${error.retriesLeft} retries left.`,
          error.message,
        );
      },
    };

    const saveBlogOperation = async () => {
      this.logger.log(`Saving blog for ${bid}-${uid} ${status}`);

      const createdBlog = new this.blogModel({
        ...payload,
        content,
        status,
        uid,
        country,
        bid,
      });

      return createdBlog.save();
    };

    try {
      return await pRetry(saveBlogOperation, retryOptions);
    } catch (error) {
      this.logger.error(`Failed to save blog for ${bid}-${uid} after all retries`, error?.message);
      throw error;
    }
  }

  async create(createBlogDto: CreateBlogDto): Promise<Blog> {
    const createdBlog = new this.blogModel(createBlogDto);
    return createdBlog.save();
  }

  async counts(bid: string, uid?: string) {
    const results = await this.blogModel.aggregate([
      { $match: { bid: String(bid), ...(uid && { uid: new mongoose.Types.ObjectId(uid) }) } },
      { $group: { _id: '$publishStatus', count: { $sum: 1 } } },
    ]);

    const draft = results.find((r) => r._id === 'draft')?.count || 0;
    const scheduled = results.find((r) => r._id === 'scheduled')?.count || 0;
    const published = results.find((r) => r._id === 'published')?.count || 0;
    const unknown = results.find((r) => r._id === null)?.count || 0;
    const total = draft + scheduled + published + unknown;

    return {
      draft,
      scheduled,
      published,
      total,
    };
  }

  async findAll(
    bid: string,
    { wordCountMax, wordCountMin, page = 1, limit = 10, q, ...params }: Partial<BlogFilterDto>,
  ): Promise<PaginatedBlogsDto> {
    // exclude unnecessary fields for list view
    const selectFields: Partial<Record<keyof Blog, 0 | 1>> = {
      keywords: 0,
      transcription: 0,
      transcriptionSummary: 0,
      blogOutline: 0,
      _blogOutline: 0,
      metaDescription: 0,
      seoKeywords: 0,
      seoResults: 0,
    };

    const filter = { bid };
    const options = {};
    let sort: any = { createdAt: -1 };

    ['identifier', 'publishStatus', 'sourceName', 'uid'].forEach((f) => {
      params[f] = params[f]?.includes(',') ? params[f].split(',') : params[f];
      if (Array.isArray(params[f])) {
        filter[f] = { $in: params[f] };
      } else if (params[f]) {
        filter[f] = decodeURI(params[f]);
      }
    });

    filter['uid'] &&= new mongoose.Types.ObjectId(filter['uid']);

    if (q) {
      filter['$text'] = { $search: q };
      options['score'] = { $meta: 'textScore' };
      sort = { score: { $meta: 'textScore' } };
    }

    if (params.publishedOn) {
      const publishedOn = params.publishedOn?.includes(',')
        ? params.publishedOn.split(',')
        : [params.publishedOn];

      filter['$and'] = publishedOn.reduce((r, p) => {
        r.push({ [`${p}Link`]: { $nin: [null, ''] } });
        return r;
      }, []);
    }

    if (wordCountMin || wordCountMax) {
      filter['wordCount'] = {};
      if (wordCountMin) {
        filter['wordCount']['$gte'] = parseInt(String(wordCountMin), 10);
      }
      if (wordCountMax) {
        filter['wordCount']['$lte'] = parseInt(String(wordCountMax), 10);
      }
    }

    const query = this.blogModel
      .find(filter, options)
      .select(selectFields)
      .sort(sort)
      .populate('uid', { name: 1, profilePicture: 1 });

    query.skip((page - 1) * limit).limit(limit);

    const [blogs, total] = await Promise.all([
      query.exec(),
      this.blogModel.countDocuments(filter).exec(),
    ]);

    const blogIds = blogs.map((blog) => blog._id);

    const [analytics, affiliateLinkTrackings] = await Promise.all([
      this.analyticsService.fetchAnalytics(bid, blogIds),
      this.affiliateLinkTrackingService.getAffiliateLinkTracking({
        bid,
        blogId: { $in: blogIds },
        conversionStatus: { $in: ['PENDING', 'PROCESSING', 'APPROVED', 'PAID'] },
      }),
    ]);

    const truncatedBlogs = blogs?.map((blog) => {
      const blogAnalytics = analytics.find((a) => String(a.blogId) === String(blog._id));

      const blogAffiliateLinkTrackings = affiliateLinkTrackings.filter(
        (alt) => String(alt.blogId) === String(blog._id),
      );
      const earnings = blogAffiliateLinkTrackings.reduce(
        (acc, alt) => acc + alt.affiliateCommission,
        0,
      );

      return {
        ...blog.toObject(),
        analytics: {
          views: blogAnalytics?.views || 0,
          clicks: blogAnalytics?.clicks || 0,
          actions: blogAffiliateLinkTrackings?.length || 0,
          earnings: earnings || 0,
        },
        content: blog?.content ? blog.content : '',
      };
    });

    return {
      data: truncatedBlogs,
      total,
    };
  }

  async findById(bid: string, id: string): Promise<Blog> {
    const blog = await this.blogModel
      .findOne({ bid, _id: id })
      .populate('uid', { name: 1, profilePicture: 1 })
      .exec();

    if (!blog) {
      throw new NotFoundException(`Blog with id ${id} not found`);
    }

    return blog;
  }

  async findByShopifyProductId(bid: string, shopifyProductId: string): Promise<Blog> {
    const blog = await this.blogModel.findOne({ bid, shopifyProductId }).exec();
    if (!blog) {
      return null;
    }
    return blog;
  }

  async shopifyProductsBlogStatus(
    bid: string,
    ids: string[] = [],
    page = 1,
    limit = 10,
  ): Promise<PaginatedBlogsDto> {
    const selectFields = {
      _id: 1,
      shopifyProductId: 1,
      status: 1,
      content: 1,
      publishAt: 1,
    };
    const idsToFilter = ids.length ? { shopifyProductId: { $in: ids } } : {};
    const query = this.blogModel
      .find({ bid, ...idsToFilter })
      .select(selectFields)
      .sort({ createdAt: -1 });

    query.skip((page - 1) * limit).limit(limit);

    const [blogs, total] = await Promise.all([
      query.exec(),
      this.blogModel.countDocuments({ bid }).exec(),
    ]);

    const data = blogs
      .filter((blog) => blog.shopifyProductId)
      .reduce((blogStatus: any, blog) => {
        const publishAt = new Date(blog.publishAt).getTime();
        const currentTime = Date.now();
        const publishTime = publishAt < currentTime ? currentTime : publishAt;
        const shopifyProductId = blog.shopifyProductId;
        blogStatus[shopifyProductId] = {
          status: blog.content
            ? 'COMPLETED'
            : currentTime - publishTime > this.tenMinutesInMilliseconds && !blog.content
              ? 'FAILED'
              : 'IN_PROGRESS',
          blogId: blog._id,
        };

        return blogStatus;
      }, {});

    return {
      data,
      total,
    };
  }

  async findBlogsByUrls(bid: string, urls: string[]) {
    if (!urls || urls.length === 0) {
      return [];
    }

    const blogs = await this.blogModel.find({ bid, url: { $in: urls } }).exec();

    if (!blogs || blogs.length === 0) {
      return [];
    }

    return blogs.reduce((blogStatus: any, blog) => {
      const publishAt = new Date(blog.publishAt).getTime();
      const currentTime = Date.now();
      const parsedUrl = new URL(blog.url);
      const videoId = parsedUrl.searchParams.get('v');

      blogStatus[videoId] = {
        status: blog.content
          ? 'COMPLETED'
          : currentTime - publishAt > this.tenMinutesInMilliseconds && !blog.content
            ? 'FAILED'
            : 'IN_PROGRESS',
        blogId: blog._id,
        platforms: blog.platforms,
        socials: blog.socials,
      };

      return blogStatus;
    }, {});
  }
  async publishSocial(bid: string, id: string, publishBlogDto: PublishBlogDto) {
    const blog: Blog = await this.blogModel.findOne({ bid, _id: id }).exec();
    const delay = new Date(publishBlogDto.social.timestamp).getTime() - Date.now();
    const { text, platform } = publishBlogDto.social;
    const job = await this.publishingService.addtoSocialQueue({
      bid: new ObjectId(bid),
      uid: new ObjectId(blog.uid),
      blogID: new ObjectId(id),
      content: text,
      platform,
      timestamp: new Date(publishBlogDto.social.timestamp),
    });
    const socials = blog.socials;
    await this.update(bid, id, {
      socials: Array.from(new Set([...socials, publishBlogDto.social])),
    });
    this.logger.log(
      `Added to ${publishBlogDto.social.platform} social media publish queue for blogId: ${id}`,
    );
    if (delay > 1.5 * 60 * 1000) return;
    else await job.finished();
  }

  async publish(bid: string, id: string, publishBlogDto: PublishBlogDto): Promise<void> {
    const blog: Blog = await this.blogModel.findOne({ bid, _id: id }).exec();
    const user: User = await this.userService.getUserByBusinessId(bid);
    if (!blog) {
      throw new NotFoundException(`Blog with id ${id} not found`);
    }
    if (publishBlogDto.platform) {
      const { sites, timestamp, platform, draft = false } = publishBlogDto.platform;
      const delay = new Date(publishBlogDto.platform.timestamp).getTime() - Date.now();
      const blogID = new ObjectId(id);
      const jobs = await this.publishingService.addToPublishQueue({
        sites,
        timestamp: timestamp ? new Date(timestamp) : new Date(),
        payload: {
          blogID,
          draft,
          platform,
        },
      });
      const platforms = blog.platforms;
      await this.update(bid, id, {
        platforms: Array.from(new Set([...platforms, publishBlogDto.platform])),
      });
      if (
        BlogPublishStatus.scheduled ===
        getPublishStatus({ platforms: [publishBlogDto.platform] } as Blog)
      ) {
        blog.publishStatus = BlogPublishStatus.scheduled;
        await blog.save();
      }
      this.logger.log(
        `Added to ${publishBlogDto.platform.platform} blog publish queue for blogId: ${id} email: ${user.email}`,
      );
      if (delay > 0) return;
      else await Promise.all(jobs.map((job) => job.finished()));
    }
  }

  async update(
    bid: string,
    id: string,
    updateBlogDto: UpdateBlogDto & { thumbnail?: string },
  ): Promise<Blog> {
    const existingBlog = await this.blogModel.findOne({ bid, _id: id });

    if (!existingBlog) {
      throw new NotFoundException(`Blog with id ${id} not found`);
    }

    Object.assign(existingBlog, updateBlogDto);

    return existingBlog.save();
  }

  async updateAssisted(
    blogId: string,
    { blogOutline }: UpdateBlogDto,
    { uid, bid, email }: User & { bid: string; uid: string },
  ): Promise<Blog> {
    let existingBlog = await this.blogModel.findOne({ bid, _id: blogId });

    if (!existingBlog) {
      throw new NotFoundException(`Blog with id ${blogId} not found`);
    }

    if (existingBlog.generationMode !== 'assisted') {
      throw new UnprocessableEntityException(
        `Blog was not generated with co-pilot generation mode.`,
      );
    }

    if (existingBlog.content) {
      throw new UnprocessableEntityException(`Blog content already generated.`);
    }

    Object.assign(existingBlog, {
      blogOutline,
      _blogOutline: existingBlog.blogOutline,
      status: 'content_generating',
    });

    existingBlog = await existingBlog.save();

    await this.generateBlogContentQueue.add(
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore handled
      {
        ...existingBlog.toObject(),
        uid,
        bid,
        email,
        blogId,
        blogTitle: existingBlog.title,
        blogOutline,
      },
      { ...JOB_OPTIONS, jobId: blogId },
    );
    this.logger.debug(`Job added to generate blog content for blog ${blogId} email ${email}`);

    return existingBlog;
  }

  async publishAssisted(
    blogId: string,
    { uid, bid, email }: User & { bid: string; uid: string },
  ): Promise<Blog> {
    const blog = await this.blogModel.findOne({ bid, _id: blogId });

    if (!blog) {
      throw new NotFoundException(`Blog with id ${blogId} not found`);
    }

    if (blog.generationMode !== 'assisted') {
      throw new UnprocessableEntityException(
        `Blog was not generated with co-pilot generation mode.`,
      );
    }

    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore handled
    const data = { ...blog.toObject(), uid, bid, email, blogId, blogTitle: blog.title };
    const blogKeywords = blog.keywords;
    const shouldGenerateAffiliateLinks =
      blogKeywords.length &&
      (blog.affiliateCommissionOptIn || Boolean(blog.affiliateCommissionOptIn) !== false);

    if (shouldGenerateAffiliateLinks) {
      await this.generateAffiliateKeywordsQueue.add(
        { ...data, blogKeywords },
        { ...JOB_OPTIONS, jobId: blogId },
      );
      this.logger.debug(`Job added to generate affiliate links for blog ${blogId} email ${email}`);
    } else {
      await this.dispatchToPublishersQueue.add(
        { ...data, shouldGenerateAffiliateLinks, blogKeywords },
        { ...JOB_OPTIONS, jobId: blogId },
      );
      this.logger.debug(`Job added to dispatch to publishers blog ${blogId} email ${email}`);
    }

    return blog;
  }

  async delete(bid: string, id: string): Promise<void> {
    const result = await this.blogModel.deleteOne({ bid, _id: id }).exec();
    if (result.deletedCount === 0) {
      throw new NotFoundException(`Blog with id ${id} not found`);
    }
  }

  getBlogTitle(content: string) {
    const $ = load(content);
    return $('h1').text();
  }

  trimBlogContent(content: string) {
    let trimmedContent: string = content;
    if (content.includes('</h1>')) {
      const titleEndIndex = content.indexOf('</h1>') + 5;
      trimmedContent = content.substring(titleEndIndex);
    }
    return trimmedContent.trim();
  }

  promptAndImagesForShopifyData(shopifyData: ShopifyProductDto): any {
    const { title, body_html, vendor, product_type, tags, variants, images } = shopifyData;

    // Format the product description by removing HTML tags
    const formattedBodyHtml = body_html?.replace(/<[^>]+>/g, '');

    let prompt = `Write a compelling blog post about the following product:
      Title: ${title}
      Description: ${formattedBodyHtml}
      Vendor: ${vendor}
      Type: ${product_type}
      Tags: ${tags}
    `;

    if (variants.length > 0) {
      prompt += `Variants:\n`;
      variants.forEach((variant) => {
        let variantDetails = `
          Variant Title: ${variant.title}
          Price: ${variant.price}
          SKU: ${variant.sku}
          Options: ${variant.option1}${variant.option2 ? ', ' + variant.option2 : ''}${
            variant.option3 ? ', ' + variant.option3 : ''
          }\n`;
        if (variant.presentment_prices && variant.presentment_prices.length > 0) {
          variant.presentment_prices.forEach((priceDetail) => {
            variantDetails += `, Price: ${priceDetail.price?.amount} ${priceDetail.price?.currency_code}`;
            if (priceDetail.compare_at_price) {
              variantDetails += `, Compare at Price: ${priceDetail.compare_at_price?.amount} ${priceDetail.compare_at_price?.currency_code}`;
            }
          });
        }
        prompt += variantDetails;
      });
    }

    const image = images?.length > 0 ? images[0].src : '';
    const contentImages = images?.length > 1 ? images.slice(1).map((image) => image.src) : [];

    return {
      prompt:
        prompt +
        '\nPlease use the above details to highlight the products unique features and benefits, ensuring the blog post is \
      factual, compelling and create an engaging narrative that would appeal to potential customers.',
      image,
      contentImages,
    };
  }

  async saveSeoKeywords(keywords: Blog['seoKeywords'], blogID: mongoose.Types.ObjectId) {
    const blog = await this.blogModel.findById(blogID);
    blog.seoKeywords = keywords;
    return blog.save();
  }

  async saveWebSearchSeoKeywords(
    keywords: Blog['webSearchSeoKeywords'],
    blogID: mongoose.Types.ObjectId,
  ) {
    const blog = await this.blogModel.findById(blogID);
    blog.webSearchSeoKeywords = keywords;
    return blog.save();
  }

  async saveLegacySeoKeywords(
    keywords: Blog['legacySeoKeywords'],
    blogID: mongoose.Types.ObjectId,
  ) {
    const blog = await this.blogModel.findById(blogID);
    blog.legacySeoKeywords = keywords;
    return blog.save();
  }

  /**
   * Find a blog by its ID
   */
  async findBlogById(blogId: string): Promise<Blog | null> {
    try {
      return await this.blogModel.findById(blogId).exec();
    } catch (error) {
      this.logger.error(`Error finding blog by ID: ${error.message}`);
      return null;
    }
  }

  /**
   * Update blog content after review
   */
  async updateBlogContent(
    blogId: string,
    updateData: { content: string; isReviewed?: boolean },
  ): Promise<Blog | null> {
    try {
      return await this.blogModel
        .findByIdAndUpdate(
          blogId,
          {
            content: updateData.content,
            isReviewed: updateData.isReviewed || true,
            updatedAt: new Date(),
          },
          { new: true },
        )
        .exec();
    } catch (error) {
      this.logger.error(`Error updating blog content after review: ${error.message}`);
      return null;
    }
  }

  /**
   * Queue a blog for review by Claude 3.7 Sonnet
   */
  async queueBlogForReview(
    blogId: string,
    content: string,
    title: string,
    options: {
      blogTone?: string;
      language?: string;
      perspective?: string;
      seoKeywords?: string[];
      blogSize?: string;
      wordCountApprox?: number;
      targetAudience?: string;
    } = {},
  ): Promise<boolean> {
    try {
      // Get the blog to make sure it exists
      const blog = await this.findBlogById(blogId);
      if (!blog) {
        this.logger.error(`Blog not found for review: ${blogId}`);
        return false;
      }

      // Add job to the queue
      await this.blogReviewQueue.add(
        'review',
        {
          blogId,
          content,
          title,
          blogTone: options.blogTone || blog.blogTone || 'conversational',
          language: options.language || blog.blogLanguage || 'English',
          perspective: options.perspective || 'second-person',
          seoKeywords: options.seoKeywords || blog.seoKeywords || [],
          wordCountApprox: options.wordCountApprox,
          targetAudience: options.targetAudience || 'general',
        },
        {
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 5000,
          },
          removeOnComplete: 50, // Keep the last 50 completed jobs
        },
      );

      this.logger.log(`Blog ${blogId} queued for review`);
      return true;
    } catch (error) {
      this.logger.error(`Error queueing blog for review: ${error.message}`);
      return false;
    }
  }
}
