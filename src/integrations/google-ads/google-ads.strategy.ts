import type { Request } from 'express';

import { PassportStrategy } from '@nestjs/passport';
import { Injectable } from '@nestjs/common';
import { Strategy } from 'passport-google-oauth20';

import config from '@/common/configs/config';

@Injectable()
export class GoogleAdsStrategy extends PassportStrategy(Strategy, 'google-ads') {
  constructor() {
    super({
      clientID: config().google.clientId,
      clientSecret: config().google.clientSecret,
      callbackURL: `${config().internalApps.blogifyAPI.url}/google-ads/callback`,
      access_type: 'offline',
      prompt: 'consent',
      scope: ['profile', 'email', 'https://www.googleapis.com/auth/adwords'],
    });
  }

  async authenticate(
    request: Request,
    options: { state: string; accessType: string; prompt: string },
  ): Promise<any> {
    options.state = JSON.stringify(request.query);
    options.accessType = 'offline';
    options.prompt = 'consent';

    return super.authenticate(request, options);
  }

  async validate(accessToken: string, refreshToken: string, profile: any, done: any): Promise<any> {
    const { email, name, picture, state } = profile._json;

    const user = {
      email,
      state,
      name,
      picture,
      accessToken,
      refreshToken,
    };
    done(null, user);
  }
}
