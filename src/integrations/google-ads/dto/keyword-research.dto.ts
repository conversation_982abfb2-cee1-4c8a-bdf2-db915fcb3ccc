import { ArrayMinSize, IsString, IsArray } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class KeywordResearchDto {
  @ApiProperty({
    description: 'URL to get keyword ideas for',
    example: 'https://blogify.ai',
  })
  @IsString()
  url: string;

  @ApiProperty({
    description: 'List of keywords to get ideas for',
    example: ['digital marketing', 'content marketing'],
    type: [String],
  })
  @IsString({ each: true })
  @ArrayMinSize(0)
  @IsArray()
  keywords: string[];

  @ApiProperty({
    description: 'Country to get keyword ideas for',
    example: 'Global',
  })
  @IsString()
  country: string;

  @ApiProperty({
    description: 'Language to get keyword ideas for',
    example: 'English',
  })
  @IsString()
  language: string;

  @ApiProperty({
    description: 'Date range to get keyword ideas for',
    example: { from: '2025-01-01', to: '2025-01-31' },
  })
  @IsString()
  date: { from: string; to: string };
}
