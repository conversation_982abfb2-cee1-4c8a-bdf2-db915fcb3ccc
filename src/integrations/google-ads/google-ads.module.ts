import { Module } from '@nestjs/common';

import { GoogleAdsStrategy } from '@/integrations/google-ads/google-ads.strategy';
import { BusinessModule } from '@/business/business.module';
import { AuthModule } from '@/auth/auth.module';
import { UserModule } from '@/user/user.module';

import { GoogleAdsController } from './google-ads.controller';
import { GoogleAdsService } from './google-ads.service';

@Module({
  imports: [BusinessModule, AuthModule, UserModule],
  controllers: [GoogleAdsController],
  providers: [GoogleAdsStrategy, GoogleAdsService],
  exports: [GoogleAdsService],
})
export class GoogleAdsModule {}
