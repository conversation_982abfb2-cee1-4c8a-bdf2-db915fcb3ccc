import {
  UnauthorizedException,
  BadRequestException,
  Controller,
  UseGuards,
  Redirect,
  Logger,
  Query,
  Post,
  Body,
  Req,
  Get,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { AuthGuard } from '@nestjs/passport';

import { AuthenticatedRequest } from '@/auth/interfaces/authenticated-request.interface';
import { QueryTokenGuard } from '@/auth/guards/query-token.guard';
import { Auth } from '@/auth/guards/auth.guard';
import config from '@/common/configs/config';

import { KeywordResearchDto } from './dto/keyword-research.dto';
import { GoogleAdsService } from './google-ads.service';

@ApiTags('Google Ads')
@ApiBearerAuth()
@Controller('google-ads')
export class GoogleAdsController {
  private readonly logger = new Logger(GoogleAdsController.name);

  constructor(private readonly googleAdsService: GoogleAdsService) {}

  @Get('connect')
  @UseGuards(QueryTokenGuard, Auth, AuthGuard('google-ads'))
  async connect() {
    return { success: true };
  }

  @Get('callback')
  @UseGuards(QueryTokenGuard, Auth, AuthGuard('google-ads'))
  @Redirect()
  async googleAdsCallback(
    @Req() { bid, user }: { bid: string; user: { refreshToken: string } },
    @Query() query: { state: string },
  ) {
    let redirectTo = `${config().internalApps.blogifyClient.url}/dashboard/`;

    try {
      redirectTo = JSON.parse(query.state).redirectTo;
    } catch (e) {
      console.log(e);
    }

    try {
      if (!user || !user.refreshToken) {
        throw new BadRequestException('Failed to get refresh token from Google Ads');
      }
      await this.googleAdsService.connect(bid, { refreshToken: user.refreshToken });
    } catch (error) {
      this.logger.error('Google Ads connect callback error:', error);
      redirectTo = `${redirectTo}?error=${encodeURIComponent(error?.message || 'Failed to connect to Google Ads')}`;
    }

    return { url: redirectTo };
  }

  @Post('disconnect')
  @UseGuards(Auth)
  async disconnect(@Req() { bid, user }: AuthenticatedRequest) {
    if (user.googleAdsStatus === 'disconnected') {
      throw new UnauthorizedException('Google Ads is not connected');
    }

    await this.googleAdsService.disconnect(bid);
    return { success: true, message: 'Google Ads disconnected successfully' };
  }

  @Get('customers')
  @UseGuards(Auth)
  async getCustomers(@Req() { bid, user }: AuthenticatedRequest) {
    if (user.googleAdsStatus === 'disconnected') {
      throw new UnauthorizedException('Google Ads is not connected');
    }

    return await this.googleAdsService.getAccessibleCustomers(bid);
  }

  @Post('customers/select')
  @UseGuards(Auth)
  async selectCustomer(
    @Req() { bid, user }: AuthenticatedRequest,
    @Body() { customerId }: { customerId: string },
  ) {
    if (user.googleAdsStatus === 'disconnected') {
      throw new UnauthorizedException('Google Ads is not connected');
    }

    if (!customerId) {
      throw new BadRequestException('Customer ID is required');
    }

    await this.googleAdsService.connect(bid, { customerId });
    return { success: true, message: 'Google Ads connected successfully' };
  }

  @Post('keyword-ideas')
  @UseGuards(Auth)
  async getKeywordIdeas(
    @Req() { bid, user }: AuthenticatedRequest,
    @Body() dto: KeywordResearchDto,
  ) {
    if (user.googleAdsStatus !== 'connected') {
      throw new UnauthorizedException('Google Ads is not connected');
    }

    return await this.googleAdsService.getKeywordIdeas(bid, dto);
  }
}
