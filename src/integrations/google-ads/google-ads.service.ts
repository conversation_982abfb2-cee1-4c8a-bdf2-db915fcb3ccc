import type { KeywordResearchDto } from './dto/keyword-research.dto';
import type { services } from 'google-ads-api';

import {
  UnauthorizedException,
  BadRequestException,
  Injectable,
  Logger,
  NotFoundException,
  InternalServerErrorException,
} from '@nestjs/common';
import { GoogleAdsApi } from 'google-ads-api';

import { BusinessService } from '@/business/business.service';
import config from '@/common/configs/config';

@Injectable()
export class GoogleAdsService {
  private readonly logger = new Logger(GoogleAdsService.name);
  private readonly googleAdsClient: GoogleAdsApi;

  constructor(private readonly businessService: BusinessService) {
    this.googleAdsClient = new GoogleAdsApi({
      client_id: config().google.clientId,
      client_secret: config().google.clientSecret,
      developer_token: config().google.ads.developerToken,
    });
  }

  async connect(
    bid: string,
    { refreshToken, customerId }: { refreshToken?: string; customerId?: string },
  ): Promise<void> {
    if (!refreshToken && !customerId) {
      throw new BadRequestException('Refresh or customer id token is required');
    }

    const business = await this.businessService.findOne(bid);
    if (!business) {
      throw new UnauthorizedException('Business not found');
    }

    if (refreshToken) {
      business.googleAdsRefreshToken = refreshToken;
    }

    if (customerId) {
      business.googleAdsCustomerId = customerId;
    }

    await business.save();
  }

  async disconnect(bid: string): Promise<void> {
    const business = await this.businessService.findOne(bid);
    if (!business) {
      throw new UnauthorizedException('Business not found');
    }

    business.googleAdsRefreshToken = null;
    business.googleAdsCustomerId = null;

    await business.save();
  }

  async getAccessibleCustomers(bid: string): Promise<string[]> {
    try {
      const { googleAdsRefreshToken } = await this.businessService.findOne(bid);
      const response = await this.googleAdsClient.listAccessibleCustomers(googleAdsRefreshToken);

      // The response contains resourceNames in the format "customers/1234567890"
      // We need to extract the customer IDs
      return response.resource_names.map((rn: string) => rn.split('/')[1]);
    } catch (error) {
      this.logger.error('Failed to get accessible customers', error);
      throw new UnauthorizedException('Failed to get accessible customers from Google Ads');
    }
  }

  async getKeywordIdeas(
    bid: string,
    { keywords, url, ...dto }: KeywordResearchDto,
  ): Promise<services.GenerateKeywordIdeaResponse> {
    const business = await this.businessService.findOne(bid);
    if (!business) {
      throw new NotFoundException('Business not found');
    }

    try {
      const customer = this.googleAdsClient.Customer({
        customer_id: business.googleAdsCustomerId,
        refresh_token: business.googleAdsRefreshToken,
        // login_customer_id: config().google.ads.loginCustomerId,
      });

      const options = {
        customer_id: business.googleAdsCustomerId,
        language: 'languageConstants/1000',
        include_adult_keywords: false,
        keyword_plan_network: 'GOOGLE_SEARCH',
        url_seed: { url },
        page_size: 10,
      } as services.GenerateKeywordIdeasRequest;

      // Filter By Keywords
      if (keywords?.length) {
        options.keyword_seed = { keywords };
      }

      // Filter By Country
      if (dto.country && dto.country !== 'Global') {
        const response = await customer.geoTargetConstants.suggestGeoTargetConstants({
          location_names: { names: [dto.country] },
          country_code: dto.country,
          toJSON: () => ({}),
        });
        options.geo_target_constants = [
          response.geo_target_constant_suggestions[0].geo_target_constant.resource_name,
        ];
      }

      // Filter By Language
      if (dto.language && dto.language !== 'Global') {
        const response = await customer.query(`
          SELECT language_constant.resource_name, language_constant.id, language_constant.code, language_constant.name
          FROM language_constant
          WHERE language_constant.code = 'en'
          LIMIT 1
        `);
        // const response = await customer.languageConstants.suggestLanguageConstants({
        //   language_names: [dto.language],
        //   toJSON: () => ({}),
        // });
        // options.language =
        //   response.language_constant_suggestions[0].language_constant.resource_name;
        console.log(response);
      }

      console.log('Options:', JSON.stringify(options, null, 2));
      const response = await customer.keywordPlanIdeas.generateKeywordIdeas(options);

      return response;
    } catch (error) {
      console.log('Google Ads Error:', JSON.stringify(error, null, 2));
      const message = this.parseGoogleAdsError(error);
      this.logger.error('Failed to get keyword ideas', error.message);
      throw new InternalServerErrorException(message || error.message);
    }
  }

  private parseGoogleAdsError(error: any): string {
    if (!error || !error.errors || !Array.isArray(error.errors)) {
      return 'Unknown error occurred.';
    }

    const parsedMessages = error.errors.map((err: any) => {
      const message = err?.message || 'No error message provided.';
      return message;
    });

    return `${parsedMessages.join('\n')}`.trim();
  }
}
