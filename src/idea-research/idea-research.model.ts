import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { ObjectId } from 'mongodb';

import { BaseModel } from '@/resources/base/base.model';
import { Business } from '@/business/business.model';
import { Project } from '@/project/project.model';

@Schema({
  timestamps: true,
  versionKey: false,
  collection: 'idea-researches',
})
export class IdeaResearch extends BaseModel {
  @Prop({ type: ObjectId, ref: 'Project' })
  project: Relation<Project>;

  @Prop({ type: String })
  prompt?: string;

  @Prop({ type: [String] })
  keywords?: string[];

  @Prop({ type: [String] })
  ideas: string[];

  @Prop({ type: String })
  researchDateTime: string;

  @Prop({ type: ObjectId, ref: 'Business' })
  business: Relation<Business>;
}

export type IdeaResearchDocument = IdeaResearch & Document;

export const IdeaResearchSchema = SchemaFactory.createForClass(IdeaResearch);
