import { BaseController } from '@/resources/base/base.controller';
import { UpdateIdeaResearchDto } from './dto/update-idea-research.dto';
import { CreateIdeaResearchDto } from './dto/create-idea-research.dto';
import { IdeaResearchService } from './idea-research.service';
import { IdeaResearch } from './idea-research.model';
import { Body, Controller, Get, Post, Query, UseGuards } from '@nestjs/common';
import { Auth } from '@/auth/guards/auth.guard';
import { RolesGuard } from '@/auth/guards/roles.guard';
import { BusinessGuard } from '@/auth/guards/business.guard';
import { GenerateIdeaDto } from './dto/generate-idea.dto';

@Controller('idea-research')
export class IdeaResearchController extends BaseController<
  IdeaResearch,
  typeof IdeaResearchService,
  CreateIdeaResearchDto,
  UpdateIdeaResearchDto
>({
  name: 'IdeaResearch',
  path: 'idea-research',
  service: IdeaResearchService,
}) {
  constructor(private readonly ideaResearchService: IdeaResearchService) {
    super();
  }

  @Get()
  @UseGuards(Auth, RolesGuard, BusinessGuard)
  async findAll(@Query('projectId') projectId: string): Promise<ListResponse<IdeaResearch>> {
    try {
      return await this.ideaResearchService.findAll(projectId);
    } catch (error) {
      throw new Error(`Failed to retrieve idea research: ${error.message}`);
    }
  }

  @Post('generate-ideas')
  @UseGuards(Auth, RolesGuard, BusinessGuard)
  async generateIdeas(@Body() dto: GenerateIdeaDto): Promise<Partial<IdeaResearch>> {
    if (!dto.prompt && !dto.keywords) {
      throw new Error('Either prompt or keywords must be provided');
    }

    try {
      return await this.ideaResearchService.generateIdeas(dto);
    } catch (error) {
      throw new Error(`Failed to generate ideas: ${error.message}`);
    }
  }
}
