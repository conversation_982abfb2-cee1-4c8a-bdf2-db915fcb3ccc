import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { IdeaResearch, IdeaResearchSchema } from './idea-research.model';
import { IdeaResearchController } from './idea-research.controller';
import { IdeaResearchService } from './idea-research.service';
import { OpenaiModule } from '@/openai/openai.module';
import { OpenaiService } from '@/openai/openai.service';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: IdeaResearch.name, schema: IdeaResearchSchema }]),
    OpenaiModule,
  ],
  controllers: [IdeaResearchController],
  providers: [IdeaResearchService, OpenaiService],
  exports: [IdeaResearchService, MongooseModule],
})
export class IdeaResearchModule {}
