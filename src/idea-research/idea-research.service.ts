import { Model } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import { BaseService } from '@/resources/base/base.service';
import { IdeaResearch } from './idea-research.model';
import { OpenaiService } from '@/openai/openai.service';
import { GenerateIdeaDto } from './dto/generate-idea.dto';

export class IdeaResearchService extends BaseService<IdeaResearch>() {
  constructor(
    @InjectModel(IdeaResearch.name) model: Model<IdeaResearch>,
    private readonly openaiService: OpenaiService,
  ) {
    super(model);
  }

  async findAll(projectId: string): Promise<ListResponse<IdeaResearch>> {
    try {
      const data = await this.model
        .find({ project: projectId, $or: [{ deleted: false }, { deleted: { $exists: false } }] })
        .sort({ researchDateTime: -1 })
        .exec();
      const total = await this.count({ project: projectId });
      return { data, total };
    } catch (error) {
      throw new Error('Failed to retrieve idea research: ' + error.message);
    }
  }

  async generateIdeas(dto: GenerateIdeaDto): Promise<Partial<IdeaResearch>> {
    try {
      const blogLanguage = 'English';
      let combinedPrompt = '';

      if (dto.prompt && dto.keywords) {
        combinedPrompt = `${dto.prompt}\nKeywords: ${dto.keywords.join(', ')}`;
      } else if (dto.prompt) {
        combinedPrompt = dto.prompt;
      } else {
        combinedPrompt = dto.keywords.join(', ');
      }

      const generatedTitles = await this.openaiService.generateBlogIdeas(
        combinedPrompt,
        blogLanguage,
        5,
      );

      const data = {
        ...dto,
        ideas: generatedTitles,
        researchDateTime: new Date().toISOString(),
      };

      return data;
    } catch (error) {
      throw new Error('Failed to generate ideas: ' + error.message);
    }
  }
}
