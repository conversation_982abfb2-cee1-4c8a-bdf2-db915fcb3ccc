// بسم الله الرحمن الرحيم

import { createParamDecorator, type ExecutionContext } from '@nestjs/common';

export const AuthInfo = createParamDecorator(
  // Requires Auth Guard
  (data: 'bid' | 'uid' | undefined, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    switch (data) {
      case 'bid':
        return request.user.business._id;
      case 'uid':
        return request.user._id;
      default:
        return request.user;
    }
  },
);
