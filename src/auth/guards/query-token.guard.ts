import type { ExecutionContext, CanActivate } from '@nestjs/common';
import type { Observable } from 'rxjs';

import { ForbiddenException, Injectable } from '@nestjs/common';

const getTokenFromState = (state: string) => {
  try {
    return JSON.parse(state).token;
  } catch (error) {
    console.log('QueryTokenGuard:', error);
    return state;
  }
};

@Injectable()
export class QueryTokenGuard implements CanActivate {
  canActivate(context: ExecutionContext): boolean | Promise<boolean> | Observable<boolean> {
    const request = context.switchToHttp().getRequest();

    if (request.headers['authorization']) {
      return request;
    }

    const token = request.query.token || getTokenFromState(request.query.state);

    if (!token) {
      throw new ForbiddenException('No auth token provided');
    }

    request.headers['authorization'] = `Bearer ${token}`;
    return request;
  }
}
