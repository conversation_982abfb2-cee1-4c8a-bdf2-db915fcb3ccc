import { Blog, BlogSchema } from '@/blog/blog.model';
import { BlogModule } from '@/blog/blog.module';
import { ScraperModule } from '@/blog/scraper.module';
import { JobModule } from '@/job/job.module';
import { Module, forwardRef } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';
import { SeoCache, SeoCacheSchema } from './cache/cache.model';
import { KeywordAnalysisProcessor } from './processors/keywordAnalysis.processor';
import { SearchRelevantContentProcessor } from './processors/searchRelevantContent.processor';
import { SeoScoringProcessor } from './processors/seoScoring.processor';
import { SeoController } from './seo.controller';
import { SeoService } from './seo.service';

@Module({
  imports: [
    JobModule,
    ConfigModule,
    ScraperModule,
    forwardRef(() => BlogModule),
    MongooseModule.forFeature([
      { name: SeoCache.name, schema: SeoCacheSchema },
      { name: Blog.name, schema: BlogSchema },
    ]),
  ],
  providers: [
    SeoService,
    SearchRelevantContentProcessor,
    KeywordAnalysisProcessor,
    SeoScoringProcessor,
  ],
  controllers: [SeoController],
  exports: [SeoService],
})
export class SeoModule {}
