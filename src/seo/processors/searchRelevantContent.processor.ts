// بسم الله الرحمن الرحيم
import { Blog, BlogQueuePayload } from '@/blog/blog.model';
import { BlogService } from '@/blog/blog.service';
import { ScraperService } from '@/blog/scraper.service';
import { JOB_OPTIONS, JOB_QUEUES } from '@/common/constants';
import { BaseProcessor } from '@/common/queue/base.processor';
import { AiSdk } from '@/llm/sdk/AiSdk';
import { GatewayService } from '@/modules/gateway/gateway.service';
import { InjectQueue, OnQueueFailed, Process, Processor } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { jsonSchema, tool } from 'ai';
import { Job, Queue } from 'bull';
import { ObjectId } from 'mongodb';
import { observeHTML, Tag } from '../functions/keywords';
import { SeoService } from '../seo.service';

@Processor(JOB_QUEUES.SEO_SEARCH_RELEVANT_CONTENT)
export class SearchRelevantContentProcessor extends BaseProcessor {
  protected readonly queueName = JOB_QUEUES.SEO_SEARCH_RELEVANT_CONTENT;
  protected readonly logger = new Logger(SearchRelevantContentProcessor.name);
  private perplexitySdk: AiSdk;
  private openaiSdk: AiSdk;

  constructor(
    @InjectQueue(JOB_QUEUES.GENERATE_BLOG_CONTENT) private readonly generateBlogContentQueue: Queue,
    @InjectQueue(JOB_QUEUES.SEO_SEARCH_RELEVANT_CONTENT) queue: Queue,
    private readonly scraperService: ScraperService,
    private readonly seoService: SeoService,
    private readonly blogService: BlogService,
    @InjectQueue(JOB_QUEUES.SEO_ANALYZE_KEYWORD) private readonly keywordAnalysisQueue: Queue,
    @InjectQueue(JOB_QUEUES.GENERATE_BLOG_OUTLINE) private readonly generateBlogOutlineQueue: Queue,
    private readonly gatewayService: GatewayService,
  ) {
    super(queue);

    // Initialize Perplexity SDK
    this.perplexitySdk = new AiSdk({
      provider: 'perplexity',
      model: 'sonar',
      identifierName: 'SearchRelevantContentProcessor',
      identifierValue: 'perplexity',
    });

    // Initialize OpenAI SDK for structured output
    this.openaiSdk = new AiSdk({
      provider: 'openai',
      model: 'gpt-4o-mini',
      identifierName: 'SearchRelevantContentProcessor',
      identifierValue: 'openai',
    });
  }

  @Process({ concurrency: 10 })
  protected async process(job: Job<BlogQueuePayload>): Promise<void> {
    return this.processWithLock(job, async (job) => {
      const USE_LEGACY_SCRAPING = process.env.USE_LEGACY_SCRAPING === 'true';
      const {
        blogTitle,
        country,
        blogId,
        uid,
        blogOutline,
        keywords,
        transcriptionSummary,
        transcription,
        prompt,
      } = job.data;

      this.gatewayService.sendBlogSeoAnalysisStatusUpdate(uid, {
        _id: blogId,
        seoAnalysisStatus: 'seo_search_content_inprogress',
      });

      this.logger.debug(`searching relevant content for blog ${blogId}`);

      // Use blogTitle if available, otherwise fallback to prompt or transcription summary
      const searchTerm = `${blogTitle ? `${blogTitle}\n` : ''}${prompt || transcriptionSummary || transcription}`;
      if (!searchTerm) {
        throw new Error(
          'No search term available (missing blogTitle, prompt, transcriptionSummary, and transcription)',
        );
      }

      let structuredSeoData: Blog['webSearchSeoKeywords'];

      if (USE_LEGACY_SCRAPING) {
        const outputs = await this.getKeywordsByTagsUsingLegacyScraping(searchTerm, country);
        if (outputs.length < 3) {
          const error = new Error('Insufficient number of search outputs for SEO');
          error['jobId'] = job.id;
          error['blogId'] = blogId;
          error['outputCount'] = outputs.length;
          throw error;
        }
        this.logger.debug(`${outputs.length} relevant results found for blog ${blogId}`);
        await this.seoService.saveToCache(outputs, blogId);

        // Extract structured data from legacy scraping results
        structuredSeoData = await this.extractStructuredDataFromLegacyScraping(
          outputs,
          searchTerm,
          transcriptionSummary || transcription || prompt,
        );
      } else {
        // Use web search approach
        structuredSeoData = await this.getKeywordsByTagsUsingWebSearch({
          blogTitle: searchTerm,
          transcriptionSummary,
          transcription,
          prompt,
        });
      }

      await this.blogService.saveWebSearchSeoKeywords(structuredSeoData, new ObjectId(blogId));
      this.logger.debug(`Saved structured SEO data for blog ID: ${blogId}`);

      await this.generateBlogOutlineQueue.add(
        {
          ...job.data,
          blogOutline,
          blogTitle,
          keywords,
          structuredSeoData,
        },
        { ...JOB_OPTIONS, jobId: blogId },
      );

      this.gatewayService.sendBlogSeoAnalysisStatusUpdate(uid, {
        _id: blogId,
        seoAnalysisStatus: 'seo_search_content_completed',
      });
    });
  }

  private async getKeywordsByTagsUsingWebSearch({
    blogTitle,
    transcriptionSummary,
    transcription,
    prompt,
  }: {
    blogTitle: string;
    transcriptionSummary: string;
    transcription: string;
    prompt: string;
  }): Promise<Blog['webSearchSeoKeywords']> {
    const sourceContext = prompt || transcriptionSummary || transcription;

    const searchTerms = await this.generateSummaryOfSearchTerms(blogTitle, sourceContext);

    const searchResults = await this.findSearchResultsUsingWebSearch(searchTerms);

    const keywordsMapping: Blog['webSearchSeoKeywords'] =
      await this.getFormattedKeywordsMapping(searchResults);

    return keywordsMapping;
  }

  private async findSearchResultsUsingWebSearch(searchTerms: string): Promise<string> {
    const systemPrompt = `
      You are a search engine optimization expert. You are given a search term.
      You need to find all the keywords that are relevant to the search term from every page of the search results.
      Also the keywords should be grouped by page tags like title, meta, h1, h2, h3 and content.
      Also find all the summary of the key findings and facts from the search results and return them as a list
      Make sure the keywords you pick are actually used in the title, meta, h1, h2, h3 and content tags and they are actual keywords between 2 to 4 words
      And the keywords are used for seo optimization of the page like long tail keywords, main keywords, secondary keywords, product keywords, service keywords, etc.
      Also summarize all the latest and most important and most relevant key findings and facts from the search results and return them as a list`;

    const prompt = `
      Extract all the relevant keywords using the Search Term: ${searchTerms}
      Return all the keywords along with minimum and maximum occurrence count based on the search results used in the title, meta, h1, h2, h3 and content grouped by the title, meta, h1, h2, h3 and content tags
      The keywords should be actual keywords that are used in the title, meta, h1, h2, h3 and content tags
      Also summarize all the key findings and facts from the search results and return them as a list

      Important Instructions:
        - For the headings find 5 to 10 keywords that are used in the heading tags and return them as a list
        - For the content find 15 to 30 keywords that are used in the content tags and return them as a list
        - Find minimum 10 key findings and facts from the search results and return them as a list
        - Make sure the keywords are between 2 to 4 words

      The output should be in the following format:
        title:
          keyword1: The Keyword
          min: 1
          max: 2
          keyword2: The Keyword
          min: 1
          max: 1
        meta:
          keyword1: The Keyword
          min: 1
          max: 2
        h1:
          keyword1: The Keyword
          min: 1
          max: 1
        h2:
          keyword1: The Keyword
          min: 1
          max: 1
        h3:
          keyword1: The Keyword
          min: 1
          max: 1
        content:
          keyword1: The Keyword
          min: 2
          max: 4
          keyword2: The Keyword
          min: 2
          max: 10
          keyword3: The Keyword
          min: 4
          max: 15
        keyFindings:
          - Facts & Findings 1
          - Facts & Findings 2
          - Facts & Findings 3
          - Facts & Findings 4
    `;
    const response = await this.perplexitySdk.generateText({
      provider: 'perplexity',
      model: 'sonar',
      prompt,
      systemPrompt,
    });

    return `Results: ${response.text}
    Sources: ${response.sources.map((source) => source.url).join(', ')}`;
  }

  private async getFormattedKeywordsMapping(searchResults: string) {
    const formatTool = tool({
      description: 'Format the search results according to the specified structure',
      parameters: jsonSchema({
        type: 'object',
        required: ['title', 'meta', 'h1', 'h2', 'h3', 'content', 'citations', 'keyFindings'],
        properties: {
          title: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                keyword: { type: 'string', description: 'The keyword text' },
                min: { type: 'number', description: 'Minimum occurrence count' },
                max: { type: 'number', description: 'Maximum occurrence count' },
              },
              required: ['keyword', 'min', 'max'],
            },
          },
          meta: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                keyword: { type: 'string', description: 'The keyword text' },
                min: { type: 'number', description: 'Minimum occurrence count' },
                max: { type: 'number', description: 'Maximum occurrence count' },
              },
              required: ['keyword', 'min', 'max'],
            },
          },
          h1: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                keyword: { type: 'string', description: 'The keyword text' },
                min: { type: 'number', description: 'Minimum occurrence count' },
                max: { type: 'number', description: 'Maximum occurrence count' },
              },
              required: ['keyword', 'min', 'max'],
            },
          },
          h2: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                keyword: { type: 'string', description: 'The keyword text' },
                min: { type: 'number', description: 'Minimum occurrence count' },
                max: { type: 'number', description: 'Maximum occurrence count' },
              },
              required: ['keyword', 'min', 'max'],
            },
          },
          h3: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                keyword: { type: 'string', description: 'The keyword text' },
                min: { type: 'number', description: 'Minimum occurrence count' },
                max: { type: 'number', description: 'Maximum occurrence count' },
              },
              required: ['keyword', 'min', 'max'],
            },
          },
          content: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                keyword: { type: 'string', description: 'The keyword text' },
                min: { type: 'number', description: 'Minimum occurrence count' },
                max: { type: 'number', description: 'Maximum occurrence count' },
              },
              required: ['keyword', 'min', 'max'],
            },
          },
          citations: {
            type: 'array',
            items: { type: 'string', description: 'Source URLs' },
          },
          keyFindings: {
            type: 'array',
            items: { type: 'string', description: 'Key Findings' },
          },
        },
      }),
    });

    const prompt = `Format the search results by calling the format_search_results tool:
    Search Results Delimited by Triple Quotes: """${searchResults}"""
    `;

    const result = await this.openaiSdk.generateText({
      provider: 'openai',
      model: 'gpt-4o-mini',
      prompt,
      tools: { format_search_results: formatTool },
    });

    // Extract the formatted results from tool calls
    let formattedResults;
    if (result.toolCalls && result.toolCalls.length > 0) {
      const toolCall = result.toolCalls[0];
      if (typeof toolCall.args === 'string') {
        formattedResults = JSON.parse(toolCall.args);
      } else {
        formattedResults = toolCall.args;
      }
    } else {
      // Fallback: try to parse from text response
      this.logger.warn('No tool calls found, attempting to parse from text response');
      try {
        // Try to extract JSON from the text response
        const jsonMatch = result.text.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          formattedResults = JSON.parse(jsonMatch[0]);
        } else {
          throw new Error('No JSON found in text response');
        }
      } catch (error) {
        this.logger.error('Failed to parse results from text response', error);
        // Return a minimal structure to prevent crashes
        formattedResults = {
          title: [],
          meta: [],
          h1: [],
          h2: [],
          h3: [],
          content: [],
          citations: [],
          keyFindings: [],
        };
      }
    }

    console.log(JSON.stringify(formattedResults, null, 2));
    return formattedResults as Blog['webSearchSeoKeywords'];
  }

  private async generateSummaryOfSearchTerms(blogTitle: string, sourceContext: string) {
    const systemPrompt = `You are tasked with generating effective search terms based on a given context. The context could be a blog transcript or a prompt. Your goal is to create search terms that will yield relevant and useful web search results related to the provided context.
    To generate effective search terms, follow these steps:

    1. Carefully read and analyze the provided context.
    2. Identify the main topic or themes present in the context.
    3. Extract key concepts, proper nouns, and important phrases that best represent the core ideas.
    4. Consider any specific questions or problems that the context might be addressing.
    5. Think about potential synonyms or related terms that could broaden the search results while maintaining relevance.

    When creating your search terms:
    - Aim for a combination of 2-5 words or a short phrase.
    - Prioritize specificity over generality to ensure more targeted results.
    - Avoid overly common words or phrases that might dilute the search results.
    - If appropriate, include industry-specific terminology or jargon that's relevant to the context.
    Additional considerations:
    - If the context is ambiguous or covers multiple topics, you may provide up to three sets of search terms, each focusing on a different aspect of the context.
    - If you encounter any proper nouns or specific concepts that you're not familiar with, include them in your search terms as they might be crucial for accurate results.
    - Remember that the goal is to find relevant web search results, so think about what terms a person might use when searching for information related to this context.
    `;

    // Check if blogTitle and sourceContext are similar or if blogTitle is just a simple prompt
    const isSimplePrompt = blogTitle === sourceContext || blogTitle.length < 10;

    const prompt = `
      Based on the following text, generate a summary of search terms that will yield relevant web search results.
      We are now in the year ${new Date().getFullYear()} so we are looking for search terms that are relevant to current information.
      If it is about a product or service, focus on search terms that can extract features, information, and pricing.
      Otherwise, focus on search terms that can extract main points and key findings.

      ${
        isSimplePrompt
          ? `Topic/Prompt: ${blogTitle}\nContent: ${sourceContext}`
          : `Blog Title: ${blogTitle}\nSource Context: ${sourceContext}`
      }`;

    const response = await this.openaiSdk.generateText({
      provider: 'openai',
      model: 'gpt-4o-mini',
      systemPrompt,
      prompt,
    });

    return response.text;
  }

  private async getKeywordsByTagsUsingLegacyScraping(blogTitle: string, country: string) {
    const htmls = await this.scraperService
      .search(blogTitle, country, 10)
      .then((urls) =>
        Promise.all(
          urls.map(
            async (url) =>
              <const>[url, await this.scraperService.fetchHTML(url).catch((e: Error) => e)],
          ),
        ),
      )
      .then((data) => new Map(data));

    const outputs = [...htmls]
      .filter((entry): entry is [URL, string] => !(entry[1] instanceof Error))
      .map(([url, html]) => <const>[url, observeHTML(html)])

      .filter(([_, parsedHTML]) => {
        // Check word count
        const words = parsedHTML
          .get('content')
          .split(/\s/)
          .filter((shard) => shard.length);
        return words.length > 500 && words.length < 4000;
      })

      .filter(([_, parsedHTML]) => {
        // Check for existence of h1 AND h2 and content
        return (<const>['h1', 'h2', 'content']).every((tag) => parsedHTML.has(tag));
      })
      .map(
        ([url, parsedHTML]) =>
          <const>[url, Object.fromEntries([...parsedHTML]) as Record<Tag, string>],
      );
    return outputs;
  }

  private async extractStructuredDataFromLegacyScraping(
    outputs: readonly (readonly [URL, Record<Tag, string>])[],
    blogTitle: string,
    sourceContext: string,
  ): Promise<Blog['webSearchSeoKeywords']> {
    // Combine all scraped content
    const allContent = outputs.map(([url, data]) => ({
      url: url.toString(),
      title: data.title || '',
      meta: data.meta || '',
      h1: data.h1 || '',
      h2: data.h2 || '',
      h3: data.h3 || '',
      content: data.content || '',
    }));

    // Extract citations (URLs)
    const citations = outputs.map(([url]) => url.toString());

    // Combine all content for AI analysis
    const combinedContent = allContent
      .map(
        (content) =>
          `URL: ${content.url}\nTitle: ${content.title}\nMeta: ${content.meta}\nH1: ${content.h1}\nH2: ${content.h2}\nH3: ${content.h3}\nContent: ${content.content.slice(0, 1000)}...`,
      )
      .join('\n\n---\n\n');

    // Use AI to extract structured keywords and key findings from scraped content
    const systemPrompt = `You are an SEO expert analyzing scraped web content. Extract structured SEO data including keywords by HTML tags and key findings from the provided content.`;

    const prompt = `Analyze the following scraped web content related to "${blogTitle}" and extract:

1. Keywords for each HTML tag (title, meta, h1, h2, h3, content) with min/max occurrence recommendations
2. Key findings and insights from the content

Context: ${sourceContext}

Scraped Content:
${combinedContent}

Format your response using the format_legacy_results tool.`;

    const formatTool = tool({
      description: 'Format legacy scraping results into structured SEO data',
      parameters: jsonSchema({
        type: 'object',
        required: ['title', 'meta', 'h1', 'h2', 'h3', 'content', 'citations', 'keyFindings'],
        properties: {
          title: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                keyword: { type: 'string' },
                min: { type: 'number' },
                max: { type: 'number' },
              },
              required: ['keyword', 'min', 'max'],
            },
          },
          meta: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                keyword: { type: 'string' },
                min: { type: 'number' },
                max: { type: 'number' },
              },
              required: ['keyword', 'min', 'max'],
            },
          },
          h1: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                keyword: { type: 'string' },
                min: { type: 'number' },
                max: { type: 'number' },
              },
              required: ['keyword', 'min', 'max'],
            },
          },
          h2: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                keyword: { type: 'string' },
                min: { type: 'number' },
                max: { type: 'number' },
              },
              required: ['keyword', 'min', 'max'],
            },
          },
          h3: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                keyword: { type: 'string' },
                min: { type: 'number' },
                max: { type: 'number' },
              },
              required: ['keyword', 'min', 'max'],
            },
          },
          content: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                keyword: { type: 'string' },
                min: { type: 'number' },
                max: { type: 'number' },
              },
              required: ['keyword', 'min', 'max'],
            },
          },
          citations: {
            type: 'array',
            items: { type: 'string' },
          },
          keyFindings: {
            type: 'array',
            items: { type: 'string' },
          },
        },
      }),
    });

    try {
      const result = await this.openaiSdk.generateText({
        provider: 'openai',
        model: 'gpt-4o-mini',
        prompt,
        systemPrompt,
        tools: { format_legacy_results: formatTool },
      });

      // Extract the formatted results from tool calls
      let structuredData;
      if (result.toolCalls && result.toolCalls.length > 0) {
        const toolCall = result.toolCalls[0];
        if (typeof toolCall.args === 'string') {
          structuredData = JSON.parse(toolCall.args);
        } else {
          structuredData = toolCall.args;
        }
      } else {
        // Fallback: create minimal structure
        this.logger.warn('No tool calls found for legacy data extraction, using fallback');
        structuredData = {
          title: [],
          meta: [],
          h1: [],
          h2: [],
          h3: [],
          content: [],
          citations: citations,
          keyFindings: [`Analysis of ${blogTitle} from ${citations.length} sources`],
        };
      }

      // Ensure citations are included
      structuredData.citations = citations;

      this.logger.debug(
        `Extracted structured data from ${outputs.length} legacy sources for ${blogTitle}`,
      );
      return structuredData as Blog['webSearchSeoKeywords'];
    } catch (error) {
      this.logger.error('Error extracting structured data from legacy scraping:', error);
      // Return fallback structure
      return {
        title: [],
        meta: [],
        h1: [],
        h2: [],
        h3: [],
        content: [],
        citations: citations,
        keyFindings: [`Analysis of ${blogTitle} from ${citations.length} sources`],
      };
    }
  }

  @OnQueueFailed()
  protected async onQueueFailed(job: Job<BlogQueuePayload>, error: Error) {
    const { blogOutline, blogTitle, keywords, blogId, uid } = job.data;

    this.gatewayService.sendBlogSeoAnalysisStatusUpdate(uid, {
      _id: blogId,
      seoAnalysisStatus: 'seo_search_content_failed',
    });

    await this.generateBlogContentQueue.add(
      { ...job.data, blogOutline, blogTitle, keywords },
      {
        ...JOB_OPTIONS,
        jobId: blogId,
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
      },
    );
    this.logger.error(
      `Job ${job.id} failed for blog ${job.data.blogId}: ${error.message}`,
      error.stack,
    );
    await super.handleFailedJob(job, error);
  }
}
