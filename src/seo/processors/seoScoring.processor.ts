import { BlogQueuePayload } from '@/blog/blog.model';
import { JOB_OPTIONS, JOB_QUEUES } from '@/common/constants';
import { BaseProcessor } from '@/common/queue/base.processor';
import { GatewayService } from '@/modules/gateway/gateway.service';
import { InjectQueue, Process, Processor } from '@nestjs/bull';
import { Job, Queue } from 'bull';
import mongoose from 'mongoose';
import { SeoService } from '../seo.service';

@Processor(JOB_QUEUES.SEO_SCORING)
export class SeoScoringProcessor extends BaseProcessor {
  protected readonly queueName = JOB_QUEUES.SEO_SCORING;

  constructor(
    @InjectQueue(JOB_QUEUES.GENERATE_AFFILIATE_KEYWORDS)
    private readonly generateAffiliateKeywordsQueue: Queue,
    @InjectQueue(JOB_QUEUES.DISPATCH_TO_PUBLISHERS)
    private readonly dispatchToPublishersQueue: Queue,
    @InjectQueue(JOB_QUEUES.SEO_SCORING) queue: Queue,
    private readonly seoService: SeoService,
    private readonly gatewayService: GatewayService,
  ) {
    super(queue);
    this.logger.log('SeoScoring processor initialized');
  }

  @Process({ concurrency: 10 })
  protected async process(job: Job<BlogQueuePayload>) {
    return this.processWithLock(job, async (job) => {
      const { blogId, uid, shouldGenerateAffiliateLinks, blogKeywords, email } = job.data;
      const blogID = new mongoose.Types.ObjectId(blogId);
      this.gatewayService.sendBlogSeoAnalysisStatusUpdate(uid, {
        _id: blogId,
        seoAnalysisStatus: 'seo_scoring_inprogress',
      });

      this.logger.debug(`Processing SEO scoring for blog ID: ${blogId}`);

      try {
        // Use simple direct scoring approach instead of complex legacy system
        const seoScore = await this.seoService.calculateSimpleSeoScore(blogID);
        this.logger.debug(`Completed SEO scoring for blog ID: ${blogId} with score: ${seoScore}%`);

        this.gatewayService.sendBlogSeoAnalysisStatusUpdate(uid, {
          _id: blogId,
          seoAnalysisStatus: 'seo_scoring_completed',
        });
      } catch (error) {
        this.gatewayService.sendBlogSeoAnalysisStatusUpdate(uid, {
          _id: blogId,
          seoAnalysisStatus: 'seo_scoring_failed',
        });

        this.logger.error({ blogId, uid, err: error }, 'Error processing SEO scoring');
        throw error;
      } finally {
        if (shouldGenerateAffiliateLinks) {
          await this.generateAffiliateKeywordsQueue.add(
            { ...job.data, blogKeywords },
            { ...JOB_OPTIONS, jobId: blogId },
          );
          this.logger.debug(
            `Job added to generate affiliate keywords for blog ${blogId} email ${email}`,
          );
        } else {
          await this.dispatchToPublishersQueue.add(
            { ...job.data, shouldGenerateAffiliateLinks, blogKeywords },
            { ...JOB_OPTIONS, jobId: blogId },
          );
          this.logger.debug(`Job added to dispatch to publishers blog ${blogId} email ${email}`);
        }
      }
    });
  }
}
