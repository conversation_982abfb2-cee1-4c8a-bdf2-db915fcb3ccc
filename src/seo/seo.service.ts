import { BlogTone } from '@/blog/blog.enums';
import { Blog } from '@/blog/blog.model';
import { OpenAiProvider } from '@/llm';
import { BaseProvider } from '@/llm/providers/base.provider';
import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectModel } from '@nestjs/mongoose';
import { Type } from '@sinclair/typebox';
import mongoose, { Model } from 'mongoose';
import pRetry from 'p-retry';
import { SeoCache } from './cache/cache.model';
import { analyseTexts, getCounts, type Tag } from './functions/keywords';

@Injectable()
export class SeoService {
  private providers: Map<string, BaseProvider>;
  private logger = new Logger(this.constructor.name);
  constructor(
    private readonly configService: ConfigService,
    @InjectModel(SeoCache.name) readonly seocache: Model<SeoCache>,
    @InjectModel(Blog.name) private readonly blogModel: Model<Blog>,
  ) {
    this.providers = new Map();
    this.providers.set(
      OpenAiProvider.PROVIDER_NAME,
      new OpenAiProvider(this.configService.get('OPENAI_API_KEY')),
    );
  }

  /**
   * Gathers SEO Results for a blog with suggested SEO keywords
   * @param blogID - The ID of the blog
   * @returns - The blog with updated SEO Results
   */
  async gatherSeoResults(blogID: mongoose.Types.ObjectId) {
    try {
      const blog = await this.blogModel.findById(blogID);
      if (!blog) {
        throw new Error(`Blog not found with ID: ${blogID} for SEO Results`);
      }

      const cachedResults = await this.seocache.find({ blogID });
      if (!cachedResults?.length) {
        throw new Error(`No cached SEO results found for blog: ${blogID}`);
      }

      const rangeChart = analyseTexts(blog.seoKeywords, cachedResults);

      blog.seoResults = {
        rangeChart: Object.fromEntries(
          [...rangeChart].map(
            ([tag, keywordRange]) => <const>[tag, Object.fromEntries([...keywordRange])],
          ),
        ) as Record<Tag, Record<string, [number, number]>>,
      };

      return await blog.save();
    } catch (error) {
      console.error(`Error gathering SEO results for blog ${blogID}:`, error);
      // Returning null instead of throwing error
      // TODO: Implement a better error handling strategy
      return null;
    }
  }

  async synthesizeHeadings(blogID: mongoose.Types.ObjectId) {
    type Heading = `h${1 | 2 | 3}`;
    try {
      const blog = await this.blogModel.findById(blogID);
      if (!blog) {
        throw new Error(`Blog not found with ID: ${blogID} for SEO Headings`);
      }

      const input = new Map<Heading, string[]>(
        (<const>['h1', 'h2', 'h3']).map((tag) => [
          tag,
          Object.keys(blog.seoResults.rangeChart[tag]),
        ]),
      );

      const suggestions = await this.suggestHeadings(input, blog.blogOutline?.summary);
      return Object.fromEntries(
        Object.entries(suggestions).map(
          ([tag, { suggestions }]) =>
            <const>[tag as Heading, { keywords: input.get(tag as Heading), suggestions }],
        ),
      );
    } catch (error) {
      this.logger.error({ err: error, blogID }, `Error synthesizing headings for blog ${blogID}`);
      throw error;
    }
  }

  /**
   * Combines blog's current keyword count with SEO data from DB
   * @param blogID - The ID of the blog
   * @returns The frequency of a keyword in the blog alongside expected range
   */
  async processBlogSeoData(blogID: mongoose.Types.ObjectId) {
    try {
      const blog = await this.blogModel.findById(blogID);
      if (!blog) {
        throw new Error(`Blog not found with ID: ${blogID}`);
      }

      if (!blog.seoResults?.rangeChart) {
        return null; // Returning null if there is no rangeChart
      }

      const rangeChart = new Map(
        Object.entries(blog.seoResults.rangeChart).map(
          ([tag, keywordRange]) => <const>[tag as Tag, new Map(Object.entries(keywordRange))],
        ),
      );

      // Build the additionalKeywords array from seoInputKeywords and blog keywords.
      const additionalKeywords = [...(blog.seoInputKeywords ?? []), ...(blog.keywords ?? [])].map(
        (keyword) => <const>[keyword, this.contentKeywordRange(blog.content, keyword)],
      );

      rangeChart.set('content', new Map([...additionalKeywords, ...rangeChart.get('content')]));

      return getCounts(blog.content, rangeChart);
    } catch (error) {
      this.logger.error(`Error scoring blog ${blogID}:`, error);
      throw error;
    }
  }

  async saveToCache(
    data: Array<readonly [URL, Record<'title' | 'content' | 'meta' | 'h1' | 'h2' | 'h3', string>]>,
    blogID: string,
  ) {
    return await this.seocache.create(
      <Array<SeoCache>>data.map(([url, parsedHTML]) => ({
        parsedHTML: parsedHTML,
        url: url.toString(),
        blogID: new mongoose.Types.ObjectId(blogID),
      })),
    );
  }

  async loadFromCache(blogID: mongoose.Types.ObjectId) {
    return new Map(
      (await this.seocache.find({ blogID })).map(
        ({ parsedHTML, url }) =>
          <const>[new URL(url), new Map(Object.entries(parsedHTML) as Array<[Tag, string]>)],
      ),
    );
  }

  async suggestContent({
    prompt,
    keywords,
    wordCount,
    summary,
    tone,
  }: {
    prompt: string;
    keywords: string[];
    wordCount: number;
    tone: BlogTone;
    summary: string;
  }): Promise<string> {
    const provider = this.providers.get(OpenAiProvider.PROVIDER_NAME);
    if (!provider) {
      throw new Error('OpenAI provider not found');
    }

    const systemPrompt = `You are an AI assistant specialized in generating SEO-optimized content.
Guidelines:
- Generate content that is exactly ${wordCount} words long
- Write in a ${tone.toLowerCase()} tone
- Each keyword should be used naturally and strategically
- Content should be:
  * Engaging and readable
  * Well-structured
  * SEO-optimized
  * Factually accurate
  * Coherent with the provided summary
- Follow SEO best practices:
  * Use keywords in first 100 words
  * Maintain keyword density without stuffing
  * Include semantic variations of keywords
  * Create natural paragraph transitions`;

    const userPrompt = `Generate SEO-optimized content based on:

Main Prompt: ${prompt}

Background Summary: ${summary}

Required Keywords: ${keywords.join(', ')}

Requirements:
1. Write exactly ${wordCount} words
2. Maintain a ${tone.toLowerCase()} tone throughout
3. Naturally incorporate all provided keywords
4. Ensure content flows logically with the background summary
5. Make content engaging and valuable to readers

Generate the content now.`;

    const ContentSchema = Type.Object({
      content: Type.String(),
    });

    try {
      const response = await provider.chatCompletion({
        messages: [{ role: 'user', content: userPrompt }],
        model: 'gpt-4o-mini',
        systemPrompt,
        tools: [
          {
            name: 'generate_content',
            description: 'Generate SEO-optimized content',
            parameters: ContentSchema,
            strict: true,
          },
        ],
      });

      if (response.message.toolCalls?.[0]?.args) {
        const result = response.message.toolCalls[0].args as { content: string };
        return result.content;
      } else throw new Error('Failed to generate SEO Content Snippet');
    } catch (error) {
      this.logger.error('Error generating SEO content with specific keywords :', error);
      throw error;
    }
  }

  async synthesizeContent(
    blogID: mongoose.Types.ObjectId,
    payload: {
      prompt: string;
      keywords: string[];
      wordCount: number;
      tone: BlogTone;
    },
  ) {
    try {
      const blog = await this.blogModel.findById(blogID);
      if (!blog) {
        throw new Error(`Blog not found with ID: ${blogID} for SEO Headings`);
      }
      return pRetry(
        () =>
          this.suggestContent({
            summary: blog.blogOutline?.summary ?? '',
            wordCount: payload.wordCount,
            keywords: payload.keywords,
            tone: payload.tone,
            prompt: payload.prompt,
          }),
        {
          retries: 3,
        },
      );
    } catch (error) {
      this.logger.error({ err: error, blogID }, `Error synthesizing content for blog ${blogID}`);
      throw error;
    }
  }
  /**
   * Uses AI to generate heading suggestions based on keyword inputs
   * @param input - A map of heading levels and their associated keywords
   * @returns An object with heading suggestions and their confidence scores
   */
  async suggestHeadings(
    input: Map<`h${1 | 2 | 3}`, string[]>,
    tldr: string,
  ): Promise<
    Record<
      'h1' | 'h2' | 'h3',
      {
        suggestions: Array<[text: string, score: number]>;
      }
    >
  > {
    const provider = this.providers.get(OpenAiProvider.PROVIDER_NAME);
    if (!provider) {
      throw new Error('OpenAI provider not found');
    }

    // Convert the input map to a more usable structure
    const keywordsInput = {
      h1: input.get('h1') || [],
      h2: input.get('h2') || [],
      h3: input.get('h3') || [],
    };

    const systemPrompt = `You are an AI assistant specialized in generating SEO-optimized headings.
  Guidelines:
  - Generate heading suggestions for H1, H2, and H3 tags based on provided keywords
  - Each heading should:
    * Incorporate the provided keywords naturally
    * Be concise and engaging
    * Follow SEO best practices
    * Be appropriate for its heading level in the content hierarchy
  - H1 headings should:
    * Be the main title (50-60 characters)
    * Include the primary keyword near the beginning
    * Be compelling and descriptive of the overall content
  - H2 headings should:
    * Be section titles (40-60 characters)
    * Include secondary keywords
    * Break down the main topic into logical subtopics
  - H3 headings should:
    * Be subsection titles (30-50 characters)
    * Include tertiary keywords
    * Further elaborate on H2 sections
  - For each heading suggestion:
    * Assign a confidence score (0-1) based on:
      - Keyword integration (how well it incorporates the keywords)
      - SEO potential (how search-friendly it is)
      - Readability and engagement
      - Hierarchical appropriateness
  - Provide 3-5 varied suggestions for each heading level
  `;

    const userPrompt = `Generate SEO-optimized heading suggestions for H1, H2, and H3 tags based on these keywords:

  H1 Keywords: ${keywordsInput.h1.join(', ')}
  H2 Keywords: ${keywordsInput.h2.join(', ')}
  H3 Keywords: ${keywordsInput.h3.join(', ')}

  For each heading level:
  1. Create 3-5 compelling heading suggestions that naturally incorporate the keywords
  2. Ensure each suggestion is appropriate for its heading level in the content hierarchy
  3. Assign confidence scores (0-1) based on keyword integration, SEO potential, and readability

    Summary of the article for which you have to suggest the headings:
    ${tldr}

  Return structured heading suggestions with confidence scores for each heading level.`;

    // Fixed schema structure - using objects instead of tuples
    const HeadingSuggestionsSchema = Type.Object({
      h1: Type.Object({
        suggestions: Type.Array(
          Type.Object({
            text: Type.String(),
            score: Type.Number({ minimum: 0, maximum: 1 }),
          }),
        ),
      }),
      h2: Type.Object({
        suggestions: Type.Array(
          Type.Object({
            text: Type.String(),
            score: Type.Number({ minimum: 0, maximum: 1 }),
          }),
        ),
      }),
      h3: Type.Object({
        suggestions: Type.Array(
          Type.Object({
            text: Type.String(),
            score: Type.Number({ minimum: 0, maximum: 1 }),
          }),
        ),
      }),
    });

    try {
      const response = await provider.chatCompletion({
        messages: [{ role: 'user', content: userPrompt }],
        model: 'gpt-4o-mini',
        systemPrompt,
        tools: [
          {
            name: 'generate_heading_suggestions',
            description: 'Generate SEO-optimized heading suggestions with confidence scores',
            parameters: HeadingSuggestionsSchema,
            strict: true,
          },
        ],
      });

      if (response.message.toolCalls?.[0]?.args) {
        const rawResult = response.message.toolCalls[0].args as Record<
          'h1' | 'h2' | 'h3',
          {
            suggestions: Array<{ text: string; score: number }>;
          }
        >;

        // Convert the object format to the tuple format required by the return type
        const convertToTupleFormat = (section: {
          suggestions: Array<{ text: string; score: number }>;
        }) => {
          return {
            suggestions: section.suggestions
              .map((item) => [item.text, item.score] as [string, number])
              .sort((a, b) => b[1] - a[1]), // Sort by score in descending order
          };
        };

        return {
          h1: convertToTupleFormat(rawResult.h1),
          h2: convertToTupleFormat(rawResult.h2),
          h3: convertToTupleFormat(rawResult.h3),
        };
      }

      // Fallback with empty results if no tool calls found
      return {
        h1: { suggestions: [] },
        h2: { suggestions: [] },
        h3: { suggestions: [] },
      };
    } catch (error) {
      this.logger.error(`Error generating heading suggestions:`, error);
      return {
        h1: { suggestions: [] },
        h2: { suggestions: [] },
        h3: { suggestions: [] },
      };
    }
  }

  /**
   * Uses AI to analyse text and suggest the topmost keywords
   * @param input -  An array of urls and their extracted body text
   * @returns An array of keywords for heading tags and content
   */
  async suggestKeywords(
    { introduction, conclusion, summary, title }: Blog['blogOutline'],
    input: Map<URL, Map<Tag, string>>,
  ): Promise<Array<{ url: string } & Record<Tag, string[]>>> {
    const provider = this.providers.get(OpenAiProvider.PROVIDER_NAME);
    if (!provider) {
      throw new Error('OpenAI provider not found');
    }

    // Process each input in parallel
    const keywordPromises = [...input].map(async ([urlObject, parsedHtml]) => {
      const url = urlObject.toString();
      const systemPrompt = `You are an AI assistant specialized in SEO keyword extraction for bloggers.
      You'll be provided the outline of a blog and some sections of what was an HTML document, and based on the outline of the blog you have to
      select relevant keywords from the HTML sections.
      Guidelines:
      - Extract keywords separately for each section (title, meta description, h1, h2, h3, and main content)
      - Keywords must appear in their respective sections
      - For each section:
        * Focus on most relevant keywords, you can decide the relevance by looking at the context which will be provided
        * Assign confidence scores (0-1) based on relevance and importance
        * Consider search intent and volume potential
        * Consider potential keywords which can improve SEO
      - Title and H1 keywords should be highly focused (1-3 keywords)
      - H2 and H3 can include supporting keywords (1-3 keywords each)
      - Content section should capture broader topic coverage (up to 10 long tail keywords)
      - Always include the main long tail keywords which is the targeted keywords of the blog
      - You can also suggest your own keywords (which might not necessarily be in the HTML) for headings only

      Information about the blog:
        - title: ${title}
        - introduction: ${introduction}
        - summary: ${summary}
        - conclusion: ${conclusion}
      `;

      const userPrompt = `Extract SEO keywords from each section of the following content.
      For each section (title, description, headers, content):
      - Extract keywords that appear in that specific section
      - Assign confidence scores based on:
        * Keyword relevance to the topic (most important)
        * Placement and prominence
        * Search potential
        * User intent match

      HTML Sections:
      Title: ${parsedHtml.get('title')}
      Meta Description: ${parsedHtml.get('meta')}
      H1: ${parsedHtml.get('h1')}
      H2: ${parsedHtml.get('h2')}
      H3: ${parsedHtml.get('h3')}
      Content: ${parsedHtml.get('content')}

      Please provide keywords and confidence scores for each section separately.`;

      const Schema = Type.Array(
        Type.Object({
          keyword: Type.String(),
          confidence: Type.Number({ minimum: 0, maximum: 1 }),
        }),
      );

      const KeywordsSchema = Type.Object({
        title: Schema,
        meta: Schema,
        h1: Schema,
        h2: Schema,
        h3: Schema,
        content: Schema,
      });

      const response = await provider.chatCompletion({
        messages: [{ role: 'user', content: userPrompt }],
        model: 'gpt-4o-mini',
        opts: {
          temperature: 0.1,
        },
        systemPrompt,
        tools: [
          {
            name: 'extract_seo_keywords',
            description: 'Extract SEO keywords from content with confidence scores',
            parameters: KeywordsSchema,
            strict: true,
          },
        ],
      });

      if (response.message.toolCalls?.[0]?.args) {
        const result = response.message.toolCalls[0].args as Record<
          Tag,
          Array<{ keyword: string; confidence: number }>
        >;

        // Process each section's keywords
        const processSection = (keywords: (typeof result)[Tag]) =>
          keywords.filter(({ confidence }) => confidence > 0.7).map(({ keyword }) => keyword);

        return {
          url,
          title: processSection(result.title),
          meta: processSection(result.meta),
          h1: processSection(result.h1),
          h2: processSection(result.h2),
          h3: processSection(result.h3),
          content: processSection(result.content),
        };
      } else throw new Error('Failed to extract keywords from the response');
    });

    return Promise.all(keywordPromises);
  }

  private contentKeywordRange(content: string, keyword: string): [min: number, max: number] {
    const escapedKeyword = keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    // case-insensitive search
    const regex = new RegExp(`\\b${escapedKeyword}\\b`, 'gi');
    const matches = content.match(regex);
    const count = matches ? matches.length : 0;

    if (count > 0) {
      // TODO: understand why getcounts is changing the value setting a lower value
      // 20% of the count as minimum
      return [Math.round(count * 0.1), Math.round(count * 0.4)];
    }
    // Fallback default range
    return [0, 1];
  }

  /**
   * Creates simplified SEO results for web search data
   * This bypasses the complex legacy scraping analysis and uses the structured web search keywords
   * @param blogID - The ID of the blog
   * @returns - The blog with updated SEO Results using web search data
   */
  async createWebSearchSeoResults(blogID: mongoose.Types.ObjectId) {
    try {
      const blog = await this.blogModel.findById(blogID);
      if (!blog) {
        throw new Error(`Blog not found with ID: ${blogID} for web search SEO Results`);
      }

      if (!blog.webSearchSeoKeywords) {
        throw new Error(`No web search SEO keywords found for blog: ${blogID}`);
      }

      // Convert web search keywords to the legacy rangeChart format
      const rangeChart: Record<Tag, Record<string, [number, number]>> = {
        title: {},
        meta: {},
        h1: {},
        h2: {},
        h3: {},
        content: {},
      };

      // Transform each tag category
      (['title', 'meta', 'h1', 'h2', 'h3', 'content'] as const).forEach((tag) => {
        if (blog.webSearchSeoKeywords[tag]?.length) {
          blog.webSearchSeoKeywords[tag].forEach((keywordItem) => {
            rangeChart[tag][keywordItem.keyword] = [keywordItem.min, keywordItem.max];
          });
        }
      });

      blog.seoResults = { rangeChart };
      return await blog.save();
    } catch (error) {
      this.logger.error(`Error creating web search SEO results for blog ${blogID}:`, error);
      return null;
    }
  }

  /**
   * Processes blog SEO data for web search approach
   * Creates a simplified scoring system compatible with legacy format
   * @param blogID - The ID of the blog
   * @returns The frequency of keywords alongside expected range for web search data
   */
  async processWebSearchBlogSeoData(blogID: mongoose.Types.ObjectId) {
    try {
      const blog = await this.blogModel.findById(blogID);
      if (!blog) {
        throw new Error(`Blog not found with ID: ${blogID}`);
      }

      if (!blog.webSearchSeoKeywords) {
        this.logger.warn(`No web search SEO keywords found for blog ${blogID}`);
        return null;
      }

      // Convert web search keywords to rangeChart format
      const rangeChart = new Map<Tag, Map<string, [number, number]>>();

      (['title', 'meta', 'h1', 'h2', 'h3', 'content'] as const).forEach((tag) => {
        const keywordMap = new Map<string, [number, number]>();

        if (blog.webSearchSeoKeywords[tag]?.length) {
          blog.webSearchSeoKeywords[tag].forEach((keywordItem) => {
            keywordMap.set(keywordItem.keyword, [keywordItem.min, keywordItem.max]);
          });
        }

        rangeChart.set(tag, keywordMap);
      });

      // Add additional keywords from blog input and outline keywords
      const additionalKeywords = [...(blog.seoInputKeywords ?? []), ...(blog.keywords ?? [])].map(
        (keyword) => <const>[keyword, this.contentKeywordRange(blog.content, keyword)],
      );

      const contentMap = rangeChart.get('content') || new Map();
      additionalKeywords.forEach(([keyword, range]) => {
        if (!contentMap.has(keyword)) {
          contentMap.set(keyword, range);
        }
      });
      rangeChart.set('content', contentMap);

      return getCounts(blog.content, rangeChart);
    } catch (error) {
      this.logger.error(`Error processing web search blog SEO data for ${blogID}:`, error);
      throw error;
    }
  }

  /**
   * Calculates a simple SEO score based on webSearchSeoKeywords
   * This replaces the complex legacy approach with a direct comparison
   * @param blogID - The ID of the blog
   * @returns A percentage score (0-100) indicating SEO optimization level
   */
  async calculateSimpleSeoScore(blogID: mongoose.Types.ObjectId): Promise<number> {
    try {
      const blog = await this.blogModel.findById(blogID);
      if (!blog) {
        throw new Error(`Blog not found with ID: ${blogID} for SEO scoring`);
      }

      if (!blog.webSearchSeoKeywords) {
        this.logger.warn(`No web search SEO keywords found for blog ${blogID}, returning 0 score`);
        return 0;
      }

      if (!blog.content) {
        this.logger.warn(`No blog content found for blog ${blogID}, returning 0 score`);
        return 0;
      }

      // Parse the blog content into HTML sections
      const parsedContent = this.parseContentIntoSections(blog.content, blog.title || '');

      let totalKeywords = 0;
      let successfulKeywords = 0;
      const sectionScores: Record<string, { score: number; total: number }> = {
        title: { score: 0, total: 0 },
        meta: { score: 0, total: 0 },
        h1: { score: 0, total: 0 },
        h2: { score: 0, total: 0 },
        h3: { score: 0, total: 0 },
        content: { score: 0, total: 0 },
      };

      // Check each section's keywords
      (['title', 'meta', 'h1', 'h2', 'h3', 'content'] as const).forEach((section) => {
        if (blog.webSearchSeoKeywords[section]?.length) {
          blog.webSearchSeoKeywords[section].forEach((keywordItem) => {
            const { keyword, min, max } = keywordItem;
            const actualCount = this.countKeywordInText(keyword, parsedContent[section]);

            totalKeywords++;
            sectionScores[section].total++;

            // Calculate keyword score based on min/max x2 range
            // The max x2 range was added for better scoring
            if (actualCount >= min && actualCount <= max * 2) {
              // Perfect match - full points
              successfulKeywords++;
              sectionScores[section].score++;
            } else if (actualCount > 0) {
              // Partial credit for keywords that appear but not in ideal range
              const penalty = Math.abs(actualCount - (min + max) / 2) / Math.max(max, actualCount);
              const partialScore = Math.max(0, 1 - penalty);
              successfulKeywords += partialScore;
              sectionScores[section].score += partialScore;
            }
            // No points for keywords that don't appear (actualCount === 0)
          });
        }
      });

      // Calculate overall score as percentage
      const overallScore = totalKeywords > 0 ? (successfulKeywords / totalKeywords) * 100 : 0;

      // Save detailed SEO results for analysis
      const seoAnalysisResults = {
        overallScore: Math.round(overallScore),
        totalKeywords,
        successfulKeywords: Math.round(successfulKeywords),
        sectionBreakdown: Object.entries(sectionScores).map(([section, data]) => ({
          section,
          score: data.total > 0 ? Math.round((data.score / data.total) * 100) : 0,
          keywordsChecked: data.total,
          keywordsOptimized: Math.round(data.score),
        })),
        keyFindings: blog.webSearchSeoKeywords.keyFindings || [],
        citations: blog.webSearchSeoKeywords.citations || [],
        timestamp: new Date(),
      };

      // Update blog with simple SEO results instead of complex rangeChart
      await this.blogModel.findByIdAndUpdate(blogID, {
        seoScore: Math.round(overallScore),
        seoAnalysisResults,
      });

      this.logger.debug(
        `Simple SEO score calculated for blog ${blogID}: ${Math.round(overallScore)}% (${Math.round(successfulKeywords)}/${totalKeywords} keywords optimized)`,
      );

      return Math.round(overallScore);
    } catch (error) {
      this.logger.error(`Error calculating simple SEO score for blog ${blogID}:`, error);
      return 0;
    }
  }

  /**
   * Parses blog content into different HTML sections for keyword analysis
   * @param content - The HTML content of the blog
   * @param title - The blog title
   * @returns Object containing text content for each section
   */
  private parseContentIntoSections(content: string, title: string): Record<string, string> {
    // Simple HTML parsing - in production you might want to use a proper HTML parser
    const titleText = title || '';
    const metaText = ''; // Meta description would need to be extracted separately if available

    // Extract headings and content using regex
    const h1Matches = content.match(/<h1[^>]*>(.*?)<\/h1>/gi) || [];
    const h2Matches = content.match(/<h2[^>]*>(.*?)<\/h2>/gi) || [];
    const h3Matches = content.match(/<h3[^>]*>(.*?)<\/h3>/gi) || [];

    // Strip HTML tags to get text content
    const stripHtml = (html: string): string => {
      return html.replace(/<[^>]*>/g, '').trim();
    };

    const h1Text = h1Matches.map(stripHtml).join(' ');
    const h2Text = h2Matches.map(stripHtml).join(' ');
    const h3Text = h3Matches.map(stripHtml).join(' ');

    // For content, strip all HTML and get plain text
    const contentText = stripHtml(content);

    return {
      title: titleText,
      meta: metaText,
      h1: h1Text,
      h2: h2Text,
      h3: h3Text,
      content: contentText,
    };
  }

  /**
   * Counts occurrences of a keyword in text (case-insensitive)
   * @param keyword - The keyword to search for
   * @param text - The text to search in
   * @returns Number of occurrences
   */
  private countKeywordInText(keyword: string, text: string): number {
    if (!keyword || !text) return 0;

    // Escape special regex characters in keyword
    const escapedKeyword = keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

    // Use word boundaries to match whole words only (case-insensitive)
    const regex = new RegExp(`\\b${escapedKeyword}\\b`, 'gi');
    const matches = text.match(regex);

    return matches ? matches.length : 0;
  }

  /**
   * Gets a blog by ID with all SEO-related fields
   * @param blogID - The ID of the blog
   * @returns The blog document with SEO data
   */
  async getBlogById(blogID: mongoose.Types.ObjectId) {
    try {
      return await this.blogModel
        .findById(blogID)
        .select('title content webSearchSeoKeywords seoAnalysisResults seoScore seoResults');
    } catch (error) {
      this.logger.error(`Error fetching blog ${blogID}:`, error);
      return null;
    }
  }
}
