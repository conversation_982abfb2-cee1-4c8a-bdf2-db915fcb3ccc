// بسم الله الرحمن الرحيم
import { Body, Controller, Get, Logger, Param, Post, UseGuards } from '@nestjs/common';
import mongoose from 'mongoose';

import { Auth } from '@/auth/guards/auth.guard';

import { BlogTone } from '@/blog/blog.enums';
import { pipe } from 'fp-ts/lib/function';
import { transformMapToRecord } from './functions/utils';
import { SeoService } from './seo.service';

@Controller('blogs/seo')
export class SeoController {
  private logger = new Logger(this.constructor.name);
  constructor(private readonly seoService: SeoService) {}

  @Post('generate/:id/content')
  @UseGuards(Auth)
  async generateContent(
    @Param('id') blogID: string,
    @Body()
    payload: {
      prompt: string;
      keywords: string[];
      wordCount: number;
      tone: BlogTone;
    },
  ) {
    return this.seoService.synthesizeContent(new mongoose.Types.ObjectId(blogID), payload);
  }
  @Post('generate/:id/headings')
  @UseGuards(Auth)
  async generateHeadings(@Param('id') blogID: string) {
    try {
      return await this.seoService.synthesizeHeadings(new mongoose.Types.ObjectId(blogID));
    } catch (error) {
      this.logger.error(
        { err: error, blogID },
        `Failed to generate SEO headings blog ID ${blogID}`,
      );
      throw error;
    }
  }

  @Get(':id')
  @UseGuards(Auth)
  async fetchSeoData(@Param('id') blogID: string) {
    try {
      // First try the new simplified approach
      const blog = await this.seoService.getBlogById(new mongoose.Types.ObjectId(blogID));

      if (blog?.seoAnalysisResults && blog?.webSearchSeoKeywords) {
        // Return structured data for new approach
        return this.transformSimplifiedSeoDataToLegacyFormat(blog);
      }

      // Fallback to legacy format processing
      return pipe(
        await this.seoService.processBlogSeoData(new mongoose.Types.ObjectId(blogID)),
        (x) => transformMapToRecord(x),
      );
    } catch (error) {
      this.logger.error({ err: error, blogID }, `Failed to fetch SEO data for blog ID ${blogID}`);
      return {
        title: {},
        meta: {},
        h1: {},
        h2: {},
        h3: {},
        content: {},
      };
    }
  }

  /**
   * Transforms new simplified SEO data back to legacy format for API compatibility
   */
  private transformSimplifiedSeoDataToLegacyFormat(blog: any) {
    const legacyFormat: Record<string, Record<string, [number, number, number]>> = {
      title: {},
      meta: {},
      h1: {},
      h2: {},
      h3: {},
      content: {},
    };

    if (!blog.webSearchSeoKeywords || !blog.content) {
      return legacyFormat;
    }

    // Parse content to get actual keyword counts
    const parsedContent = this.parseContentForCounts(blog.content, blog.title || '');

    // Transform each section
    (['title', 'meta', 'h1', 'h2', 'h3', 'content'] as const).forEach((section) => {
      if (blog.webSearchSeoKeywords[section]?.length) {
        blog.webSearchSeoKeywords[section].forEach((keywordItem: any) => {
          const { keyword, min, max } = keywordItem;
          const actualCount = this.countKeywordInSection(keyword, parsedContent[section]);

          legacyFormat[section][keyword] = [actualCount, min, max];
        });
      }
    });

    return legacyFormat;
  }

  /**
   * Parses blog content into different sections for counting
   */
  private parseContentForCounts(content: string, title: string) {
    // Simple HTML parsing - in production you might want to use a proper HTML parser
    const h1Matches = content.match(/<h1[^>]*>(.*?)<\/h1>/gi) || [];
    const h2Matches = content.match(/<h2[^>]*>(.*?)<\/h2>/gi) || [];
    const h3Matches = content.match(/<h3[^>]*>(.*?)<\/h3>/gi) || [];
    const metaMatches = content.match(/<meta[^>]*content=['"]([^'"]*)['"]/gi) || [];

    // Remove HTML tags for content counting
    const cleanContent = content.replace(/<[^>]*>/g, ' ');

    return {
      title: title,
      meta: metaMatches.join(' ').replace(/<[^>]*>/g, ' '),
      h1: h1Matches.join(' ').replace(/<[^>]*>/g, ' '),
      h2: h2Matches.join(' ').replace(/<[^>]*>/g, ' '),
      h3: h3Matches.join(' ').replace(/<[^>]*>/g, ' '),
      content: cleanContent,
    };
  }

  /**
   * Counts keyword occurrences in a text section
   */
  private countKeywordInSection(keyword: string, text: string): number {
    if (!keyword || !text) return 0;

    const escapedKeyword = keyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const regex = new RegExp(`\\b${escapedKeyword}\\b`, 'gi');
    const matches = text.match(regex);
    return matches ? matches.length : 0;
  }
}
