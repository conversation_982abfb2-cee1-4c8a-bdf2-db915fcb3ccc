import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { Project, ProjectSchema } from './project.model';
import { ProjectController } from './project.controller';
import { ProjectService } from './project.service';
import { DataForSeoApiService } from '@/common/services/data-for-seo-api.service';
import { IdeaModule } from '@/idea/idea.module';

@Module({
  imports: [MongooseModule.forFeature([{ name: Project.name, schema: ProjectSchema }]), IdeaModule],
  controllers: [ProjectController],
  providers: [ProjectService, DataForSeoApiService],
  exports: [ProjectService, MongooseModule],
})
export class ProjectModule {}
