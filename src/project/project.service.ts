import { Model } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import { BaseService } from '@/resources/base/base.service';
import { Project } from './project.model';
import { CreateProjectDto } from './dto/create-project.dto';
import { DataForSeoApiService } from '@/common/services/data-for-seo-api.service';
import { getDomainFromUrl, normalizeUrl } from '@/common/utils/url';
import { IdeaService, WebsiteMetadata } from '@/idea/idea.service';

export class ProjectService extends BaseService<Project>() {
  constructor(
    @InjectModel(Project.name) model: Model<Project>,
    private readonly dataForSeoApiService: DataForSeoApiService,
    private readonly ideaService: IdeaService,
  ) {
    super(model);
  }

  async create(dto: CreateProjectDto): Promise<Project> {
    try {
      const websiteUrl = normalizeUrl(dto.websiteUrl);
      const domain = getDomainFromUrl(dto.websiteUrl);

      if (!domain) {
        throw new Error('Please provide a valid website domain');
      }

      const domainAnalytics = await this.dataForSeoApiService.fetchDomainAnalytics(domain);
      const scrapData: WebsiteMetadata = await this.ideaService.scrapeWebsite(websiteUrl);

      const newProject = {
        ...dto,
        websiteUrl: websiteUrl,
        domainAnalytics,
        title: scrapData.title,
        description: scrapData.description,
        keywords: scrapData.keywords,
        headings: scrapData.headings,
        content: scrapData.content,
        featuredImage: scrapData.featuredImage,
      };

      return super.create(newProject);
    } catch (error) {
      throw new Error(`Failed to create project: ${error.message}`);
    }
  }
}
